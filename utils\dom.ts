/**
 * Checks if an element is a descendant of another element.
 *
 * @param parent The potential parent element.
 * @param child The potential child element.
 * @returns True if the child is a descendant of the parent, false otherwise.
 */
export function isDescendant(parent: HTMLElement, child: HTMLElement): boolean {
 if (parent == child) return true // is the same element
  let node = child.parentNode;
  while (node != null) {
    if (node === parent) {
      return true;
    }
    node = node.parentNode;
  }
  return false;
}
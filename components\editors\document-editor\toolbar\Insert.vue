<template>
  <div class="insert-toolbar-tab">
    <div class="insert-toolbar-col">
      <div class="flex">
        <Button
          label="Link"
          show-label
          icon-size="xl"
          icon="heroicons:link"
          command="link"
          @click="setURL"
        />
        <Button
          label="Image"
          show-label
          icon-size="xl"
          icon="heroicons:photo"
          command="image"
          @click="setImage"
        />
        <!-- <Button
          label="Video"
          show-label
          icon-size="xl"
          icon="heroicons:video-camera"
          command="video"
        />
        <Button
          label="Audio"
          show-label
          icon-size="xl"
          icon="heroicons:microphone"
          command="audio"
        /> -->
        <!-- <Button
          label="Symbol"
          show-label
          icon-size="xl"
          icon="heroicons:at-symbol"
          command="symbol"
        />
        <Button
          label="Date"
          show-label
          icon-size="xl"
          icon="heroicons:calendar"
          command="date"
        />
        <Button
          label="Time"
          show-label
          icon-size="xl"
          icon="heroicons:clock"
          command="time"
        /> -->
      </div>
    </div>
    <Divider />
    <div class="insert-toolbar-col">
      <div class="flex">
        <Button
          label="Table"
          show-label
          icon-size="xl"
          icon="heroicons:table-cells"
          command="table"
          @click="insertTable"
        />
        <Button
          label="Chart"
          show-label
          icon-size="xl"
          icon="ic:baseline-addchart"
          command="chart"
          @click="insertChart"
        />
        <!-- <Button
          label="Formula"
          show-label
          icon-size="xl"
          icon="ri:formula"
          command="formula"
        /> -->
            <DropDownButton
          label="Columns"
          show-label
          icon="heroicons:view-columns"
          icon-size="xl"
          width="318px"
          :splited="false"
        
          @select="console.log"
        >
          <template #dropdown="{close}" >
            <div class="p-2">
               <ColumnsSelect @select="selectCols($event); close()"/>
            </div>
          </template>
        </DropDownButton>
        <!-- <Button
          label="Columns"
          show-label
          icon-size="xl"
          icon="heroicons:view-columns"
          command="columns"
        /> -->
        <!-- <Button
          label="Variables"
          show-label
          icon-size="xl"
          icon="material-symbols:variable-add-outline"
          command="variables"
        /> -->
      </div>
    </div>
    <Divider />
    <div class="insert-toolbar-col">
      <div class="flex">
        <Button
          label="Page Break"
          show-label
          icon-size="xl"
          icon="fluent:document-page-break-24-regular"
          command="pageBreak"
        />
        <Button
          label="Line Break"
          show-label
          icon-size="xl"
          icon="icon-park-outline:paragraph-break-two"
          command="lineBreak"
          @click="executeCommand"
        />
        <Button
          label="Divider"
          show-label
          icon-size="xl"
          icon="radix-icons:divider-horizontal"
          command="divider"
          @click="executeCommand"
        />
      </div>
    </div>
    <Divider />
    <div class="insert-toolbar-col">
      <div class="flex">
        <Button
          label="QR Code"
          show-label
          icon-size="xl"
          icon="heroicons:qr-code"
          command="qrCode"
        />
        <Button
          label="Barcode"
          show-label
          icon-size="xl"
          icon="material-symbols:barcode"
          command="barcode"
        />
      </div>
    </div>
    <Divider />
    <div class="insert-toolbar-col">
      <div class="flex">
        <Button
          label="E-Signature"
          show-label
          icon-size="xl"
          icon="ant-design:signature-outlined"
          command="eSignature"
        />
        <Button
          label="Official Seal"
          show-label
          icon-size="xl"
          icon="icon-park-outline:seal"
          command="office"
        />
      </div>
    </div>
    <!-- <Divider />
    <div class="insert-toolbar-col">
      <div class="flex">
        <Button
          label="Template"
          show-label
          icon-size="xl"
          icon="icon-park-outline:page-template"
          command="template"
        />
      </div>
    </div> -->
  </div>
</template>
<script setup lang="ts">
import Button from "./Button.vue"; // Import Button component
import Divider from "./Divider.vue"; // Import Divider component
import ColumnsSelect from "./ColumnsSelect.vue"; // Import Divider component
import DropDownButton from "./DropDownButton.vue";

import { useDocEditor } from "~/app/shared/composables/core/editors/useDocEditor";
import { useGlobalModal } from "~/composables/useGlobalModal.ts"; // Import globalModal composable
import InsertLinkModal from "../modals/InsertLinkModal.vue";
import InsertImageModal from "../modals/InsertImageModal.vue";
import InsertChartModal from "../modals/InsertChartModal.vue";
 
const { openModal } = useGlobalModal(); // Get the openModal function from globalModal composable

const { executeCommand } = useDocEditor();

const setURL = () => {
  // Add your logic for handling page settings here
  const modal = openModal({
    title: "Insert Link",
    component: InsertLinkModal,
    icon: "i-heroicons:link",
    okText: "OK",
    cancelText: "Cancel",
    onCancel() {
      modal.close();
    },
    onOk({ component }) {
      component.submit();
      component.emitter.once("submit", ({ url }) => {
        if (url === null) return;
        if (url === "") {
          executeCommand("unsetLink");
          // this.editor.chain().focus().extendMarkRange("link").unsetLink().run();
          return;
        }
        executeCommand("setLink", { href: url });
        // this.editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()

        modal.close();
      });
    },
  });
};

const setImage = () => {
  // Add your logic for handling page settings here
  const modal = openModal({
    title: "Insert Image",
    component: InsertImageModal,
    icon: "heroicons:photo",
    okText: "OK",
    cancelText: "Cancel",
    onCancel() {
      modal.close();
    },
    onOk({ component }) {
      component.emitter.once("change", (url) => {
        if (url === null || url === "") return;

        executeCommand("setImage", { src: url });

        modal.close();
      });
      component.getFile();
    },
  });
};
const emit = defineEmits(["insert-table"]);
const insertTable = () => {
  emit("insert-table");
};

const insertChart = () => {
  const modal = openModal({
    title: "Insert Chart",
    component: InsertChartModal,
    icon: "ic:baseline-addchart",
    // width: 960,
    size: "xl",
    okText: "OK",
    cancelText: "Cancel",
    onCancel() {
      modal.close();
    },
    onOk({ component }) {
      console.log(component);

      // component.emitter.once("change", (url) => {
      //   if (url === null || url === "") return;

      //   executeCommand("setImage", { src: url });

      //   modal.close();
      // });
      // component.getFile();
    },
  });
};

const selectCols = cols => {
  executeCommand('insertColumns', cols)
};

</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.insert-toolbar-tab {
  @apply flex align-middle justify-center;
}
.insert-toolbar-col {
  @apply flex flex-col space-y-2 align-middle justify-center;
}
</style>

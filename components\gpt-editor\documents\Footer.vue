<template>
  <div class="editor-footer">
    <!-- <ui-button variant="flat" color="gray" @click="$emit('toggle-sidebar')">open -></ui-button> -->
    <div>Page 1 of 1</div>
    <div>Words: {{ words }}</div>
    <div>Characters: {{ characters }}</div>
    <div>Last edited: Just now</div>
    <!-- <ui-button variant="flat" color="gray"  @click="$emit('toggle-panel')"> <- open </ui-button> -->
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useDocEditor } from "~/app/shared/composables/core/editors/useDocEditor";
const { editor } = useDocEditor()

const words = computed(() => editor.value ? editor.value.storage.characterCount.words() : 0)
const characters = computed(() => editor.value ? editor.value.storage.characterCount.characters() : 0)
 
</script>
<style scoped>
@reference '~/assets/css/tailwind.css';
.editor-footer {
  @apply border-t border-gray-200  bg-gray-50  flex  items-start px-4 py-2 shadow-sm justify-between text-gray-500;
}
</style>

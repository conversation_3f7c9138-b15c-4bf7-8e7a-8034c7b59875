<template>
  <div class="h-screen bg-gray-100 flex flex-col">
    <!-- Toolbar -->
    <GptEditorDocumentsToolbar  />
    <!-- Main Content Layout -->
    <div class="flex flex-1 overflow-hidden relative">
      <!-- Collapsed Sidebar (Icons Only or Narrow Menu) -->
      <GptEditorDocumentsSidebar :opened="sidebar" />

      <!-- Page Styled Content Area -->
      <GptEditorDocumentsCanvas  ref="canvasRef" :sidebar-opened="sidebar" :panel-opened="panel"  />

      <!-- Collapsed Secondary Panel (Icons Only or Narrow Menu) -->
      <GptEditorDocumentsSecondaryPanel :opened="panel" />
    </div>

    <!-- Footer -->
    <GptEditorDocumentsFooter @toggle-sidebar="handelToggle('sidebar')"  @toggle-panel="handelToggle('panel')"  />


  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const sidebar = ref(false)
const panel = ref(false)
const handelToggle = (panelName: string) => {
   switch (panelName) {
    case 'panel':
      panel.value = !panel.value
      break;
   
    default:
      sidebar.value = !sidebar.value
      break;
   }
}
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
</style>

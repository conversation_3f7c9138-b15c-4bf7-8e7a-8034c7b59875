import { useModal, type ModalConfig, useModalManager } from '../app/shared/composables/ui/useModal'
import { ComponentSize } from '../app/shared/types'
import type { Component } from 'vue'
import { ref, type Ref, nextTick, markRaw } from 'vue'
import { useEventBus } from '../app/shared/composables/core/useEventBus'

interface GlobalModalOptions extends Omit<ModalConfig, 'id'> {
  component?: Component
  props?: Record<string, any>
  content?: string
  cancelText?: string
  okText?: string
}

interface EnhancedModalInstance {
  id: string
  close: (reason?: 'close' | 'cancel' | 'ok') => Promise<void>
}

export function useGlobalModal() {
  const { emit } = useEventBus()
  const { getModal } = useModalManager()

  const openModal = (options: GlobalModalOptions): EnhancedModalInstance => {
    const modalId = `global-modal-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

    const modalConfig: ModalConfig & GlobalModalOptions = {
      ...options,
      id: modalId,
      component: options.component ? markRaw(options.component) : undefined,
    }

    emit('open-modal', modalConfig)

    const close = async (reason: 'close' | 'cancel' | 'ok' = 'close') => {
      const modal = getModal(modalId)
      if (modal && modal.config.onClose) {
        modal.config.onClose()
      }
    }

    return {
      id: modalId,
      close,
    }
  }

  /**
   * Open a confirmation modal
   */
  const openConfirmModal = (
    title: string,
    message: string,
    options: {
      confirmText?: string
      cancelText?: string
      type?: 'info' | 'warning' | 'error' | 'success'
      onConfirm?: () => void | Promise<void>
      onCancel?: () => void
    } = {}
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      const modal = useModal({
        title,
        content: message,
        size: ComponentSize.SM,
        closable: true,
        maskClosable: false,
        okText: options.confirmText,
        cancelText: options.cancelText,
        onOk: async () => {
          if (options.onConfirm) {
            await options.onConfirm()
          }
          resolve(true)
        },
        onCancel: () => {
          if (options.onCancel) {
            options.onCancel()
          }
          resolve(false)
        },
        onClose: () => {
          resolve(false)
        }
      } as ModalConfig & GlobalModalOptions)

      modal.open()
    })
  }

  /**
   * Open an alert modal
   */
  const openAlertModal = (
    title: string,
    message: string,
    type: 'info' | 'warning' | 'error' | 'success' = 'info'
  ): Promise<void> => {
    return new Promise((resolve) => {
      const modal = useModal({
        title,
        content: message,
        size: ComponentSize.SM,
        closable: true,
        maskClosable: true,
        onClose: () => {
          resolve()
        }
      } as ModalConfig & GlobalModalOptions)

      modal.open()
    })
  }

  /**
   * Open a custom modal with HTML content
   */
  const openCustomModal = (
    title: string,
    content: string,
    options: Partial<ModalConfig> = {}
  ) => {
    const modal = useModal({
      title,
      content,
      size: ComponentSize.MD,
      closable: true,
      maskClosable: true,
      ...options
    } as ModalConfig & GlobalModalOptions)

    modal.open()
    return modal
  }

  return {
    openModal,
    openConfirmModal,
    openAlertModal,
    openCustomModal
  }
}

export { ComponentSize };
// Export types for external use
export type { GlobalModalOptions }

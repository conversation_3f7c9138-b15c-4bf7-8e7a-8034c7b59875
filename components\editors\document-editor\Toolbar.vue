<template>
  <div class="document-editor-toolbar" :class="toolbarClasses">
    <!-- Tab Navigation -->
    <div class="toolbar-tabs">
      <button v-for="tab in toolbarTabs" :key="tab.key" @click="activeTab = tab.key"
        :class="['toolbar-tab', { active: activeTab === tab.key }]">
        {{ t(`documents.editor.toolbar.tabs.${tab.key}`) }}
      </button>
    </div>

    <!-- Toolbar Content -->
    <div class="toolbar-content" v-show="showToolbar">
      <a href="javascript:void(0)" @click="scrollLeft" class="toolbar-arrow toolbar-arrow-left" v-if="showLeftArrow">
        <Icon name="heroicons:chevron-left" />
      </a>
      <div class="toolbar-content-holder" ref="toolbarContentHolderRef">
        <template v-for="tab in toolbarTabs" :key="tab.key">
          <div class="toolbar-panel" v-if="activeTab === tab.key">
            <component :is="TabsComponentMap[tab.key]" ref="tabRef"  @mounted="mounted" />
          </div>
        </template>
      </div>
      <a href="javascript:void(0)" class="toolbar-arrow toolbar-arrow-right" @click="scrollRight" v-if="showRightArrow">
        <Icon name="heroicons:chevron-right" />
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useScroll } from '@vueuse/core'
import { onMounted, reactive, useTemplateRef } from 'vue'
import { ref, computed, defineAsyncComponent, watch, nextTick } from "vue";
import { useI18n } from "vue-i18n";

// Props
interface Props {
  editor?: any;
  modelValue?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: string];
  command: [command: string, params?: any];
}>();

// Composables
const { t, locale } = useI18n();

const tabRef = ref(null);
// RTL languages
const rtlLanguages = ["he", "ar"];
const isRTL = computed(() => rtlLanguages.includes(locale.value));

const activeTab = ref("home");

const showToolbar = ref(true);

const toolbarContentHolderRef = useTemplateRef<HTMLElement>('toolbarContentHolderRef')
const { x, measure, arrivedState } = useScroll(toolbarContentHolderRef,  { behavior: 'smooth' })


const TabsComponentMap = {
  home: defineAsyncComponent(() => import("./toolbar/Home.vue")),
  insert: defineAsyncComponent(() => import("./toolbar/Insert.vue")),
  table: defineAsyncComponent(() => import("./toolbar/Table.vue")),
  tools: defineAsyncComponent(() => import("./toolbar/Tools.vue")),
  page: defineAsyncComponent(() => import("./toolbar/Page.vue")),
  export: defineAsyncComponent(() => import("./toolbar/Export.vue")),
};

// Toolbar configuration
const toolbarTabs = [
  { key: "home", label: "Home" },
  { key: "insert", label: "Insert" },
  { key: "table", label: "Table" },
  { key: "tools", label: "Tools" },
  { key: "page", label: "Page" },
  { key: "export", label: "Export" },
];

const toolbarClasses = computed(() => ({
  rtl: isRTL.value,
  ltr: !isRTL.value,
}));

 

 
 
 

 

const insertTable = () => {
  console.log('insert-table');
};


 

</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.document-editor-toolbar {
  @apply bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex-1;
}

.toolbar-status-bar {
  @apply flex items-center justify-between px-4 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
}

.status-left {
  @apply flex items-center gap-2;
}

.status-indicator {
  @apply flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400;
}

.status-right {
  @apply flex items-center gap-2;
}

.toolbar-tabs {
  @apply flex items-center border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900;
}

.toolbar-tab {
  @apply px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 border-b-2 border-transparent transition-colors duration-200;
}

.toolbar-tab.active {
  @apply text-blue-600 dark:text-blue-400 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20;
}

.toolbar-content {
  @apply bg-gray-50 dark:bg-gray-800 relative   w-[100vw] ;

}
.toolbar-content-holder {
  @apply flex flex-col overflow-hidden relative w-[100vw];
}
.toolbar-arrow {
  @apply absolute top-[10%]  flex items-center justify-center w-6 h-[80%] rounded-sm shadow-xs border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-900 z-50 hover:bg-brandPrimary-500 hover:text-white hover:border-brandPrimary-600;
}

.toolbar-arrow-left {
  @apply left-2;
}

.toolbar-arrow-right {
  @apply right-2;
}

.toolbar-panel {
  @apply p-1 flex;
}

.toolbar-row {
  @apply flex items-start gap-4 mb-4 last:mb-0;
}

.toolbar-group {
  @apply flex flex-col gap-2;
}

.group-label {
  @apply text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide;
}

.group-buttons {
  @apply flex items-center gap-2;
}

.toolbar-separator {
  @apply w-px h-12 bg-gray-300 dark:bg-gray-600;
}

.font-controls {
  @apply flex gap-2 mb-2;
}

.font-select,
.font-size-select,
.style-select {
  @apply px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.font-select {
  @apply w-32;
}

.font-size-select {
  @apply w-16;
}

.style-select {
  @apply w-24;
}

.format-buttons,
.alignment-buttons,
.list-buttons {
  @apply flex gap-1;
}

.button-row {
  @apply flex gap-1;
}
/* RTL Support */
/* .rtl .toolbar-tabs {
  @apply flex-row-reverse;
}

.rtl .toolbar-row {
  @apply flex-row-reverse;
}

.rtl .group-buttons {
  @apply flex-row-reverse;
}

.rtl .font-controls {
  @apply flex-row-reverse;
}

.rtl .format-buttons,
.rtl .alignment-buttons,
.rtl .list-buttons {
  @apply flex-row-reverse;
} */
</style>

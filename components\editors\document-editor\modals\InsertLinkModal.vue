<template>
  <div class="flex flex-col w-[450px]">
    <forms-form :schema="schema"  ref="formRef" :show-errors="false" @submit="handleSubmit" />
  </div>
</template>
<script setup lang="ts">
import mitt, { Emitter, Handler } from 'mitt';

// Extend the mitt emitter to include a 'once' method
type Events = Record<string, unknown>;
interface MittWithOnce extends Emitter<Events> {
  once: (type: string, handler: <PERSON><PERSON>) => void;
}
const emitter = mitt() as MittWithOnce;

emitter.once = function (type: string, handler: Handler) {
  const onceHandler: Handler = (event) => {
    handler(event);
    emitter.off(type, onceHandler);
  };
  emitter.on(type, onceHandler);
};

import { ref, useTemplateRef, shallowRef } from 'vue'
import { z } from 'zod'
// Update the type to 'any' or the actual component type if available
const formRef = useTemplateRef<any>('formRef')
const schema = ref([
    { name: "url", label: "URL", component: "UiInput", rules: z.string().min(1, "URL is required.").url('Insert a valid URL') },
])
 const submit = () => {
  if (!formRef.value) return
  formRef.value?.submitForm()
 }
const emit = defineEmits(['submit'])
 
 
const handleSubmit = values => emitter.emit('submit', values)
defineExpose({
  submit,
  emitter
})
</script>
<template>
  <div class="toolbar-content">
    <div class="toolbar-tabs">
      <UiTabs
        :tabs="tabs"
        size="sm"
        hide-border
        hide-content
        v-model="activeTab"
      ></UiTabs>
    </div>
    <div class="toolbar-tabs-content" ref="tabsContentRef">
      <a
        href="javascript:void(0)"
        @click="scrollLeft"
        class="toolbar-arrow toolbar-arrow-left"
        v-if="showLeftArrow"
      >
        <Icon name="heroicons:chevron-left" />
      </a>
      <component :is="activeComponent" v-if="activeComponent" @insert-table="insertTable" ref="activeComponentRef" @ready="ready"></component>
      <ui-alert title="No active component" type="warning" v-else></ui-alert>
      <a
        href="javascript:void(0)"
        class="toolbar-arrow toolbar-arrow-right"
        @click="scrollRight"
        v-if="showRightArrow"
      >
        <Icon name="heroicons:chevron-right" />
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, useTemplateRef, ref, watch,  nextTick } from "vue";
import { useScroll, useResizeObserver } from "@vueuse/core";

import Home from "@/components/editors/document-editor/toolbar/Home.vue";
import Insert from "@/components/editors/document-editor/toolbar/Insert.vue";
import Table from "@/components/editors/document-editor/toolbar/Table.vue";
import Tools from "@/components/editors/document-editor/toolbar/Tools.vue";
import Page from "@/components/editors/document-editor/toolbar/Page.vue";
import Export from "@/components/editors/document-editor/toolbar/Export.vue";

// tabs
const activeTab = ref("home");
const tabs = [
  {
    label: "Home",
    value: "home",
    component: Home,
  },
  {
    label: "Insert",
    value: "insert",
    component: Insert,
  },
  {
    label: "Table",
    value: "table",
    component: Table,
  },
  {
    label: "Tools",
    value: "tools",
    component: Tools,
  },
  {
    label: "Page",
    value: "page",
    component: Page,
  },
  {
    label: "Export",
    value: "export",
    component: Export,
  },
];

const activeComponent = computed(() => {
  const currentTab = tabs.find((tab) => tab.value === activeTab.value);
  return currentTab ? currentTab?.component : undefined;
});

//scroll
const tabsContentRef = useTemplateRef<HTMLElement>("tabsContentRef");
const { x, measure, arrivedState } = useScroll(tabsContentRef, {
  behavior: "smooth",
});
const increaseBy = ref(0);
useResizeObserver(tabsContentRef, (entries) => {
  const entry = entries[0];
  const { width } = entry.contentRect;
  increaseBy.value = width;
});

const showLeftArrow = computed(() => {
  return !arrivedState.left;
});
const showRightArrow = computed(() => {
  return !arrivedState.right;
});

const scrollLeft = () => {
  if (showLeftArrow.value) {
    x.value -= increaseBy.value;
    // nextTick(() => measure());
  }
};

const scrollRight = () => {
  if (showRightArrow.value) {
    x.value += increaseBy.value;
    // nextTick(() => measure());
  }
};
watch([increaseBy, activeTab], () => {
  nextTick(() => {
    measure();
  });
});
import type { ComponentPublicInstance } from "vue";

type InsertTableComponent = ComponentPublicInstance & {
  insertTable?: () => void;
};
const hasInsertTable = ref(false) 
const activeComponentRef = useTemplateRef<InsertTableComponent>('activeComponentRef')
const insertTable = () => {
  activeTab.value = 'table'
  hasInsertTable.value = true
}

const ready = () => {

  if ( hasInsertTable.value && activeTab.value == 'table' && activeComponentRef.value && typeof activeComponentRef.value.insertTable === 'function') {
     activeComponentRef.value.insertTable()
     hasInsertTable.value = false
  }
   
}
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.toolbar-content {
  @apply relative border-b border-gray-200;
}
.toolbar-tabs {
  @apply flex items-center bg-white px-4 pb-2;
}
.toolbar-tabs-content {
  @apply flex px-4 py-2 w-[100vw] bg-gray-50 overflow-hidden min-h-[112px];
}
.toolbar-arrow {
  @apply absolute top-[33%]  flex items-center justify-center w-6 h-[60%] rounded-sm shadow-xs border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-900 z-50 hover:bg-brandPrimary-500 hover:text-white hover:border-brandPrimary-600;
}

.toolbar-arrow-left {
  @apply left-2;
}

.toolbar-arrow-right {
  @apply right-2;
}
</style>

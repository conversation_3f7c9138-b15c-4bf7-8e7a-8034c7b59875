/* Tiptap Pages Extension Styles */

/* Container for the pages editor */
.tiptap-pages-container {
  --page-width: 794px;
  --page-height: 1123px;
  --content-width: 754px;
  --content-height: 1023px;
  --page-margin-top: 20px;
  --page-margin-right: 20px;
  --page-margin-bottom: 20px;
  --page-margin-left: 20px;
  --header-height: 50px;
  --footer-height: 50px;
  --page-gap: 50px;
  --page-break-background: #f6f3f4;
  
  background: var(--page-break-background);
  padding: var(--page-gap) 0;
  min-height: 100vh;
}

/* Page styles */
.tiptap-page {
  position: relative;
  margin: 0 auto var(--page-gap);
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  overflow: hidden;
  page-break-after: always;
  break-after: page;
  display: flex;
  flex-direction: column;
  width: var(--page-width);
  height: var(--page-height);
}

/* Page format specific styles */
.tiptap-page-a4 {
  width: 794px;
  height: 1123px;
}

.tiptap-page-a3 {
  width: 1123px;
  height: 1587px;
}

.tiptap-page-a5 {
  width: 559px;
  height: 794px;
}

.tiptap-page-letter {
  width: 816px;
  height: 1063px;
}

.tiptap-page-legal {
  width: 816px;
  height: 1346px;
}

.tiptap-page-tabloid {
  width: 1063px;
  height: 1634px;
}

/* Page header styles */
.tiptap-page-header {
  height: var(--header-height);
  padding: 8px var(--page-margin-right) 8px var(--page-margin-left);
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
  box-sizing: border-box;
  flex-shrink: 0;
}

.tiptap-page-header-left {
  justify-content: flex-start;
}

.tiptap-page-header-center {
  justify-content: center;
}

.tiptap-page-header-right {
  justify-content: flex-end;
}

/* Page content styles */
.tiptap-page-content {
  flex: 1;
  min-height: var(--content-height);
  max-height: var(--content-height);
  overflow: hidden;
  padding: var(--page-margin-top) var(--page-margin-right) var(--page-margin-bottom) var(--page-margin-left);
  box-sizing: border-box;
  position: relative;
}

.tiptap-page-content-overflow {
  border-right: 3px solid #ef4444;
}

.tiptap-page-content-overflow::after {
  content: "Content overflow detected";
  position: absolute;
  top: 10px;
  right: 10px;
  background: #ef4444;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  z-index: 10;
}

/* Page footer styles */
.tiptap-page-footer {
  height: var(--footer-height);
  padding: 8px var(--page-margin-right) 8px var(--page-margin-left);
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
  box-sizing: border-box;
  flex-shrink: 0;
}

.tiptap-page-footer-left {
  justify-content: flex-start;
}

.tiptap-page-footer-center {
  justify-content: center;
}

.tiptap-page-footer-right {
  justify-content: flex-end;
}

/* Page break styles */
.tiptap-page-break {
  height: var(--page-gap);
  background: var(--page-break-background);
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  page-break-before: always;
  break-before: page;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tiptap-page-break:hover {
  background: color-mix(in srgb, var(--page-break-background) 80%, #000 20%);
}

.tiptap-page-break-indicator {
  background: white;
  border: 2px dashed #d1d5db;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.tiptap-page-break:hover .tiptap-page-break-indicator {
  border-color: #9ca3af;
  background: #f9fafb;
}

.tiptap-page-break-auto .tiptap-page-break-indicator {
  border-color: #3b82f6;
  color: #3b82f6;
}

.tiptap-page-break-manual .tiptap-page-break-indicator {
  border-color: #d1d5db;
  color: #6b7280;
}

/* Print styles */
@media print {
  .tiptap-pages-container {
    background: white;
    padding: 0;
  }
  
  .tiptap-page {
    box-shadow: none;
    border-radius: 0;
    margin: 0;
    page-break-after: always;
    break-after: page;
  }
  
  .tiptap-page-break {
    display: none;
  }
  
  .tiptap-page-content-overflow::after {
    display: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tiptap-pages-container {
    --page-break-background: #1f2937;
  }
  
  .tiptap-page {
    background: #111827;
    color: #f9fafb;
  }
  
  .tiptap-page-header,
  .tiptap-page-footer {
    background: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }
  
  .tiptap-page-break-indicator {
    background: #1f2937;
    border-color: #4b5563;
    color: #d1d5db;
  }
  
  .tiptap-page-break:hover .tiptap-page-break-indicator {
    background: #374151;
    border-color: #6b7280;
  }
}

/* Responsive design */
@media (max-width: 1200px) {
  .tiptap-pages-container {
    padding: 20px;
  }
  
  .tiptap-page {
    transform: scale(0.8);
    transform-origin: top center;
    margin-bottom: calc(var(--page-gap) - var(--page-height) * 0.2);
  }
}

@media (max-width: 768px) {
  .tiptap-page {
    transform: scale(0.6);
    margin-bottom: calc(var(--page-gap) - var(--page-height) * 0.4);
  }
}

@media (max-width: 480px) {
  .tiptap-page {
    transform: scale(0.4);
    margin-bottom: calc(var(--page-gap) - var(--page-height) * 0.6);
  }
}

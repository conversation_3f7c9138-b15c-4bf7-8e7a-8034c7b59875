import type { PageFormat, CustomPageFormat, PageMargins } from './types'
import { PAGE_FORMATS } from './formats'

/**
 * Auto-detects the DPI of the current environment.
 * @returns The detected DPI, or a default of 96.
 */
function getDPI(): number {
  if (typeof window === 'undefined') {
    return 96
  }

  const dpi = window.devicePixelRatio || 1
  return dpi * 96
}

/**
 * Converts centimeters to pixels.
 * @param cm - The value in centimeters.
 * @param dpi - The DPI to use for conversion. Auto-detected if not provided.
 * @returns The equivalent value in pixels.
 */
export function cmToPixels(cm: number, dpi?: number): number {
  const d = dpi || getDPI()
  return Math.round((cm / 2.54) * d)
}

/**
 * Converts inches to pixels.
 * @param inches - The value in inches.
 * @param dpi - The DPI to use for conversion. Auto-detected if not provided.
 * @returns The equivalent value in pixels.
 */
export function inchToPixels(inches: number, dpi?: number): number {
  const d = dpi || getDPI()
  return Math.round(inches * d)
}

/**
 * Converts pixels to centimeters.
 * @param pixels - The value in pixels.
 * @param dpi - The DPI to use for conversion. Auto-detected if not provided.
 * @returns The equivalent value in centimeters.
 */
export function pixelsToCm(pixels: number, dpi?: number): number {
  const d = dpi || getDPI()
  return (pixels * 2.54) / d
}

/**
 * Converts pixels to inches.
 * @param pixels - The value in pixels.
 * @param dpi - The DPI to use for conversion. Auto-detected if not provided.
 * @returns The equivalent value in inches.
 */
export function pixelsToInch(pixels: number, dpi?: number): number {
  const d = dpi || getDPI()
  return pixels / d
}

/**
 * Gets a page format by name or returns the custom format.
 * @param format - The format name or custom format object.
 * @returns The page format.
 */
export function getPageFormat(format: string | CustomPageFormat): PageFormat {
  if (typeof format === 'string') {
    if (!(format in PAGE_FORMATS)) {
      throw new Error(`Invalid page format: ${format}`)
    }
    return PAGE_FORMATS[format]
  }
  return format
}

/**
 * Creates a custom page format.
 * @param name - The name of the custom format.
 * @param width - The width in pixels.
 * @param height - The height in pixels.
 * @param margins - The margins in pixels.
 * @returns The custom page format.
 */
export function createCustomFormat(
  name: string,
  width: number,
  height: number,
  margins: PageMargins
): CustomPageFormat {
  return {
    name,
    width,
    height,
    margins,
  }
}

/**
 * Creates a custom page format from centimeters.
 * @param name - The name of the custom format.
 * @param widthCm - The width in centimeters.
 * @param heightCm - The height in centimeters.
 * @param marginsCm - The margins in centimeters.
 * @returns The custom page format.
 */
export function createCustomFormatFromCm(
  name: string,
  widthCm: number,
  heightCm: number,
  marginsCm: { top: number; right: number; bottom: number; left: number }
): CustomPageFormat {
  return createCustomFormat(
    name,
    cmToPixels(widthCm),
    cmToPixels(heightCm),
    {
      top: cmToPixels(marginsCm.top),
      right: cmToPixels(marginsCm.right),
      bottom: cmToPixels(marginsCm.bottom),
      left: cmToPixels(marginsCm.left),
    }
  )
}

/**
 * Creates a custom page format from inches.
 * @param name - The name of the custom format.
 * @param widthInch - The width in inches.
 * @param heightInch - The height in inches.
 * @param marginsInch - The margins in inches.
 * @returns The custom page format.
 */
export function createCustomFormatFromInch(
  name: string,
  widthInch: number,
  heightInch: number,
  marginsInch: { top: number; right: number; bottom: number; left: number }
): CustomPageFormat {
  return createCustomFormat(
    name,
    inchToPixels(widthInch),
    inchToPixels(heightInch),
    {
      top: inchToPixels(marginsInch.top),
      right: inchToPixels(marginsInch.right),
      bottom: inchToPixels(marginsInch.bottom),
      left: inchToPixels(marginsInch.left),
    }
  )
}

/**
 * Calculates page dimensions including headers, footers, and gaps.
 * @param format - The page format.
 * @param headerHeight - The header height in pixels.
 * @param footerHeight - The footer height in pixels.
 * @param pageGap - The gap between pages in pixels.
 * @returns The calculated dimensions.
 */
export function calculatePageDimensions(
  format: string | CustomPageFormat,
  headerHeight: number = 50,
  footerHeight: number = 50,
  pageGap: number = 50
) {
  const pageFormat = getPageFormat(format)
  const { width, height, margins } = pageFormat

  const contentWidth = width - margins.left - margins.right
  const contentHeight = height - margins.top - margins.bottom - headerHeight - footerHeight

  return {
    pageWidth: width,
    pageHeight: height,
    contentWidth,
    contentHeight,
    totalHeight: height + pageGap,
    margins,
    headerHeight,
    footerHeight,
    pageGap,
  }
}

/**
 * Generates CSS custom properties for a page format.
 * @param format - The page format.
 * @param options - Additional layout options.
 * @returns CSS custom properties as a string.
 */
export function generatePageCSS(
  format: string | CustomPageFormat,
  options: {
    headerHeight?: number
    footerHeight?: number
    pageGap?: number
    pageBreakBackground?: string
  } = {}
): string {
  const dimensions = calculatePageDimensions(
    format,
    options.headerHeight,
    options.footerHeight,
    options.pageGap
  )

  return `
    --page-width: ${dimensions.pageWidth}px;
    --page-height: ${dimensions.pageHeight}px;
    --content-width: ${dimensions.contentWidth}px;
    --content-height: ${dimensions.contentHeight}px;
    --page-margin-top: ${dimensions.margins.top}px;
    --page-margin-right: ${dimensions.margins.right}px;
    --page-margin-bottom: ${dimensions.margins.bottom}px;
    --page-margin-left: ${dimensions.margins.left}px;
    --header-height: ${dimensions.headerHeight}px;
    --footer-height: ${dimensions.footerHeight}px;
    --page-gap: ${dimensions.pageGap}px;
    --page-break-background: ${options.pageBreakBackground || '#f6f3f4'};
  `.trim()
}

/**
 * Debounce function for performance optimization.
 * @param func - The function to debounce.
 * @param wait - The number of milliseconds to delay.
 * @returns The debounced function.
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Throttle function for performance optimization.
 * @param func - The function to throttle.
 * @param limit - The number of milliseconds to limit.
 * @returns The throttled function.
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

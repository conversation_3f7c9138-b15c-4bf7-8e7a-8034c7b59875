import { Node, mergeAttributes } from '@tiptap/core'
import type { PageBreakAttributes } from './types'

export const PageBreak = Node.create({
  name: 'pageBreak',
  
  group: 'block',
  content: '',
  isolating: true,
  draggable: false,
  selectable: true,
  atom: true,

  addAttributes() {
    return {
      type: {
        default: 'manual',
        parseHTML: element => element.getAttribute('data-break-type') || 'manual',
        renderHTML: attributes => ({
          'data-break-type': attributes.type,
        }),
      },
      pageNumber: {
        default: 1,
        parseHTML: element => parseInt(element.getAttribute('data-page-number') || '1'),
        renderHTML: attributes => ({
          'data-page-number': attributes.pageNumber,
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="page-break"]',
        priority: 100,
      },
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    const attrs = node.attrs as PageBreakAttributes
    
    return [
      'div',
      mergeAttributes(HTMLAttributes, {
        'data-type': 'page-break',
        'class': `tiptap-page-break tiptap-page-break-${attrs.type}`,
        'style': `
          height: var(--page-gap, 50px);
          background: var(--page-break-background, #f6f3f4);
          margin: 0;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          page-break-before: always;
          break-before: page;
        `,
      }),
      [
        'div',
        {
          'class': 'tiptap-page-break-indicator',
          'style': `
            background: white;
            border: 2px dashed #d1d5db;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
          `,
        },
        [
          'svg',
          {
            'width': '16',
            'height': '16',
            'viewBox': '0 0 24 24',
            'fill': 'none',
            'stroke': 'currentColor',
            'stroke-width': '2',
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
          },
          [
            'path',
            {
              'd': 'M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z',
            },
          ],
          [
            'polyline',
            {
              'points': '14,2 14,8 20,8',
            },
          ],
          [
            'line',
            {
              'x1': '16',
              'y1': '13',
              'x2': '8',
              'y2': '13',
            },
          ],
          [
            'line',
            {
              'x1': '16',
              'y1': '17',
              'x2': '8',
              'y2': '17',
            },
          ],
          [
            'polyline',
            {
              'points': '10,9 9,9 8,9',
            },
          ],
        ],
        `Page Break ${attrs.type === 'auto' ? '(Auto)' : '(Manual)'}`,
      ],
    ]
  },

  addNodeView() {
    return ({ node, HTMLAttributes, getPos, editor }) => {
      const dom = document.createElement('div')
      const attrs = node.attrs as PageBreakAttributes
      
      // Set up the container
      Object.assign(dom, {
        ...HTMLAttributes,
        'data-type': 'page-break',
        className: `tiptap-page-break tiptap-page-break-${attrs.type}`,
      })
      
      // Apply styles
      dom.style.cssText = `
        height: var(--page-gap, 50px);
        background: var(--page-break-background, #f6f3f4);
        margin: 0;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        page-break-before: always;
        break-before: page;
        cursor: pointer;
        transition: all 0.2s ease;
      `
      
      // Create indicator
      const indicator = document.createElement('div')
      indicator.className = 'tiptap-page-break-indicator'
      indicator.style.cssText = `
        background: white;
        border: 2px dashed #d1d5db;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;
      `
      
      // Add icon
      const icon = document.createElement('div')
      icon.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14,2 14,8 20,8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
      `
      
      // Add text
      const text = document.createElement('span')
      text.textContent = `Page Break ${attrs.type === 'auto' ? '(Auto)' : '(Manual)'}`
      
      indicator.appendChild(icon)
      indicator.appendChild(text)
      dom.appendChild(indicator)
      
      // Add hover effects
      dom.addEventListener('mouseenter', () => {
        indicator.style.borderColor = '#9ca3af'
        indicator.style.backgroundColor = '#f9fafb'
      })
      
      dom.addEventListener('mouseleave', () => {
        indicator.style.borderColor = '#d1d5db'
        indicator.style.backgroundColor = 'white'
      })
      
      // Add click handler for selection
      dom.addEventListener('click', () => {
        if (typeof getPos === 'function') {
          const pos = getPos()
          editor.commands.setNodeSelection(pos)
        }
      })

      return {
        dom,
      }
    }
  },

  addCommands() {
    return {
      insertPageBreak: () => ({ commands, state }) => {
        const { selection } = state
        const { $from } = selection
        
        // Get current page number
        let pageNumber = 1
        state.doc.descendants((node, pos) => {
          if (node.type.name === 'page' && pos < $from.pos) {
            pageNumber = Math.max(pageNumber, (node.attrs.pageNumber || 0) + 1)
          }
        })

        const pageBreakContent = {
          type: 'pageBreak',
          attrs: {
            type: 'manual',
            pageNumber,
          },
        }

        return commands.insertContent(pageBreakContent)
      },
    }
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Enter': () => this.editor.commands.insertPageBreak(),
      'Ctrl-Shift-Enter': () => this.editor.commands.insertPageBreak(),
    }
  },
})

<template>

    <ui-tooltip size="sm" variant="light" :content="props.label" :disabled="props.disabled" :show-tooltip="!props.showLabel">
        <template #trigger="on">
            <ui-button size="sm" variant="flat" color="gray" :disabled="props.disabled" :fullWidth="fullWidth"
            :class="props.class"
               v-bind="on"
                @click.prevent.stop="handleClick">
               
                <div class="flex flex-col justify-center items-center">
                 <slot v-if="$slots.default" />
                <Icon v-else :size="iconSize" :color="props.iconColor" :name="icon" />
                <span v-if="props.showLabel" class="mt-1 text-xs">{{ props.label }}</span>
                
                </div>
                
            </ui-button>
        </template>
    </ui-tooltip>
</template>
<script setup lang="ts">
import { computed } from 'vue'
interface Props {
  command?: string;
  disabled?: boolean;
  label?: string;
  icon?: string;
  iconSize?: string;
  iconColor?: string;
  showLabel?: boolean;
  class?: any;
  fullWidth?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  command: "",
  disabled: false,
  label: "",
  icon: "",
  iconSize: "sm",
  iconColor: "inherit",
  showLabel: false,
  class: "",
});

const iconSizeMap = {
  xs: "calc(var(--spacing) * 3)",
  sm: "calc(var(--spacing) * 4)",
  md: "calc(var(--spacing) * 5)",
  lg: "calc(var(--spacing) * 6)",
  xl: "calc(var(--spacing) * 7)",
  "2xl": "calc(var(--spacing) * 8)",
};

const iconSize = computed(() => {
  return iconSizeMap[props.iconSize];
});

const emit = defineEmits(['click'])
const handleClick = (event: MouseEvent) => {

  if (props.disabled ) return;
  emit('click', props.command)
};
</script>

import { cmToPixels, inchToPixels } from './utils'
import type { PageFormat } from './types'

// Built-in page formats with proper dimensions and margins
export const PAGE_FORMATS: Record<string, PageFormat> = {
  A4: {
    width: cmToPixels(21.0),
    height: cmToPixels(29.7),
    margins: {
      top: cmToPixels(2.5),
      right: cmToPixels(2.0),
      bottom: cmToPixels(2.5),
      left: cmToPixels(2.0),
    },
  },
  A3: {
    width: cmToPixels(29.7),
    height: cmToPixels(42.0),
    margins: {
      top: cmToPixels(2.5),
      right: cmToPixels(2.0),
      bottom: cmToPixels(2.5),
      left: cmToPixels(2.0),
    },
  },
  A5: {
    width: cmToPixels(14.8),
    height: cmToPixels(21.0),
    margins: {
      top: cmToPixels(2.0),
      right: cmToPixels(1.5),
      bottom: cmToPixels(2.0),
      left: cmToPixels(1.5),
    },
  },
  Letter: {
    width: inchToPixels(8.5),
    height: inchToPixels(11),
    margins: {
      top: inchToPixels(1),
      right: inchToPixels(1),
      bottom: inchToPixels(1),
      left: inchToPixels(1),
    },
  },
  Legal: {
    width: inchToPixels(8.5),
    height: inchToPixels(14),
    margins: {
      top: inchToPixels(1),
      right: inchToPixels(1),
      bottom: inchToPixels(1),
      left: inchToPixels(1),
    },
  },
  Tabloid: {
    width: inchToPixels(11),
    height: inchToPixels(17),
    margins: {
      top: inchToPixels(1),
      right: inchToPixels(1),
      bottom: inchToPixels(1),
      left: inchToPixels(1),
    },
  },
}

// Format validation
export function isValidFormat(format: string): boolean {
  return format in PAGE_FORMATS
}

// Get format with error handling
export function getPageFormat(format: string): PageFormat {
  if (!isValidFormat(format)) {
    throw new Error(`Invalid page format: ${format}. Available formats: ${Object.keys(PAGE_FORMATS).join(', ')}`)
  }
  return PAGE_FORMATS[format]
}

// Generate CSS for a page format
export function getFormatCSS(format: string | PageFormat): string {
  const pageFormat = typeof format === 'string' ? getPageFormat(format) : format
  const { width, height, margins } = pageFormat
  const { top, right, bottom, left } = margins

  return `
    width: ${width}px;
    height: ${height}px;
    padding: ${top}px ${right}px ${bottom}px ${left}px;
    box-sizing: border-box;
  `.trim()
}

// Get content dimensions (excluding margins)
export function getContentDimensions(format: string | PageFormat): { width: number; height: number } {
  const pageFormat = typeof format === 'string' ? getPageFormat(format) : format
  const { width, height, margins } = pageFormat

  return {
    width: width - margins.left - margins.right,
    height: height - margins.top - margins.bottom,
  }
}

<template>
  <div class="relative">
    <!-- Enhanced Label -->
    <label v-if="label" :for="id" :class="[
        'block text-xs font-semibold transition-colors duration-200 mb-2',
        error ? 'text-brandDanger' : 'text-gray-700 dark:text-gray-300',
        required ? 'after:content-[\'*\'] after:text-brandDanger after:ml-1' : ''
      ]">
      {{ label }}
    </label>

    <!-- Select Container -->
    <UiDropdown ref="dropdownRef" width="full">
      <template #trigger="{ isOpen, toggle }">
        <div class="relative group">
          <div v-if="leadingIcon" class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
            <Icon :name="leadingIcon" size="calc(var(--spacing) * 4)" :class="[
                ' transition-colors duration-200',
                error ? 'text-brandDanger' : 'text-gray-400 group-focus-within:text-brandPrimary'
              ]" />
          </div>
        
          <button
            type="button"
            ref="selectRef"
            @click="toggle"
            @keydown.esc="dropdownRef?.close()"
            @keydown.space.prevent="toggle"
            @keydown.enter.prevent="toggle"
            @keydown.up.prevent="focusPreviousOption"
            @keydown.down.prevent="focusNextOption"
            :class="[
                'block text-left transition-all duration-200 ease-in-out appearance-none',
                'border rounded-md leading-5 font-medium',
                'focus:outline-none focus:ring-2 focus:ring-offset-0',
                'sm:text-sm cursor-pointer',
                widthClasses,
                sizeClasses,
                stateClasses,
                leadingIcon ? 'pl-10' : 'pl-4',
                'pr-10',
            ]"
            :disabled="disabled"
            aria-haspopup="listbox"
            :aria-expanded="isOpen"
            :aria-labelledby="id"
          >
            <span class="block truncate">
                {{ selectedOption ? selectedOption.label : placeholder || '&nbsp;' }}
            </span>
          </button>

          <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <Icon name="material-symbols:expand-more"
              size="calc(var(--spacing) * 4)" :class="[
                'transition-all duration-200',
                error ? 'text-brandDanger' : 'text-gray-400 group-focus-within:text-brandPrimary',
                isOpen ? 'rotate-180' : ''
              ]" />
          </div>

          <div v-if="loading" class="absolute inset-y-0 right-8 pr-3 flex items-center">
            <UiSpinner size="xs" class="text-brandPrimary" />
          </div>

          <div :class="[
              'absolute inset-0 rounded-md pointer-events-none transition-all duration-200',
              'ring-0 ring-brandPrimary/20'
            ]" />
        </div>
      </template>

      <template #content="{ close }">
        <ul
            ref="optionsListRef"
            class="overflow-auto text-base max-h-60 focus:outline-none sm:text-sm"
            tabindex="-1"
            role="listbox"
            :aria-activedescendant="focusedOptionIndex > -1 ? `option-${focusedOptionIndex}` : undefined"
        >
            <template v-if="hasGroups">
                <template v-for="(group, groupIndex) in groupedOptions" :key="group.label || `group-${groupIndex}`">
                    <div v-if="group.label" class="px-4 py-2 text-xs font-bold text-gray-500 uppercase dark:text-gray-400">
                        {{ group.label }}
                    </div>
                    <li
                        v-for="(option, optionIndex) in group.options"
                        :key="option.value"
                        :id="`option-${getFlatOptionIndex(groupIndex, optionIndex)}`"
                        role="option"
                        @click="selectOption(option, close)"
                        @mouseenter="focusOption(getFlatOptionIndex(groupIndex, optionIndex))"
                        :class="[
                            isSelected(option) ? selectedClass : '',
                            option.font ? `font-(family:${option.font})` : '',
                            'flex justify-between  relative cursor-pointer select-none py-2 pr-4 transition-colors duration-150',
                            showCheckIcon ? 'pl-10' : 'pl-4',
                            isOptionFocused(getFlatOptionIndex(groupIndex, optionIndex)) ? 'text-white bg-brandPrimary' : 'text-gray-900 dark:text-gray-200',
                            option.disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-brandPrimary/10 hover:text-brandPrimary dark:hover:bg-brandPrimary/20'
                        ]"
                        :aria-selected="isSelected(option)"
                    >
                        <span :class="['block truncate', isSelected(option) ? 'font-semibold' : 'font-normal']"
                          :style="[option.font ? `font-family: ${option.font}` : '']"
                        >
                            {{ option.label }} 
                        </span>
                        <span v-if="option.description"  class="text-gray-400">{{ option.description }} </span>
                        <span
                            v-if="isSelected(option) && showCheckIcon"
                            :class="['absolute inset-y-0 left-0 flex items-center pl-3', isOptionFocused(getFlatOptionIndex(groupIndex, optionIndex)) ? 'text-white' : 'text-brandPrimary']"
                        >
                            <Icon name="material-symbols:check" size="calc(var(--spacing) * 4)" />
                        </span>
                    </li>
                </template>
            </template>
            <template v-else>
                 <li
                    v-for="(option, index) in options"
                    :key="option.value"
                    :id="`option-${index}`"
                    role="option"
                    @click="selectOption(option, close)"
                    @mouseenter="focusOption(index)"
                    :class="[
                        isSelected(option) ? selectedClass : '',
                        option.font ? `font-(family:${option.font})` : '',
                        'flex justify-between relative cursor-pointer select-none py-2 pr-4 transition-colors duration-150',
                        showCheckIcon ? 'pl-10' : 'pl-4',
                        isOptionFocused(index) ? 'text-white bg-brandPrimary' : 'text-gray-900 dark:text-gray-200',
                        option.disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-brandPrimary/10 hover:text-brandPrimary dark:hover:bg-brandPrimary/20'
                    ]"
                    :aria-selected="isSelected(option)"
                >
                    <span :class="['block truncate', isSelected(option) ? 'font-semibold': 'font-normal']"
                      :style="[option.font ? `font-family: ${option.font}` : '']"
                    >
                        {{ option.label }}
                    </span>
                    <span v-if="option.description" class="text-gray-400" >{{ option.description }} </span>
                    <span
                        v-if="isSelected(option) && showCheckIcon"
                        :class="['absolute inset-y-0 left-0 flex items-center pl-3', isOptionFocused(index) ? 'text-white' : 'text-brandPrimary']"
                    >
                        <Icon name="material-symbols:check" size="calc(var(--spacing) * 4)" />
                    </span>
                </li>
            </template>
        </ul>
      </template>
    </UiDropdown>

    <!-- Enhanced Error Message -->
    <Transition name="error-slide">
      <div v-if="error" class="mt-2 flex items-start space-x-2">
        <Icon name="material-symbols:error" size="calc(var(--spacing) * 4)" class=" text-brandDanger flex-shrink-0 mt-0.5" />
        <p class="text-sm text-brandDanger font-medium">{{ error }}</p>
      </div>
    </Transition>

    <!-- Enhanced Help Text -->
    <p v-if="helpText && !error" class="mt-2 text-sm text-gray-500 dark:text-gray-400 flex items-start space-x-2">
      <Icon v-if="helpTextIcon" name="material-symbols:info" size="calc(var(--spacing) * 4)" class=" flex-shrink-0 mt-0.5" />
      <span class="text-xs">{{ helpText }}</span>
    </p>

    <!-- Success State -->
    <Transition name="success-slide">
      <div v-if="showSuccess && !error && modelValue" class="mt-2 flex items-center space-x-2">
        <Icon name="material-symbols:check-circle" size="calc(var(--spacing) * 4)" class=" text-brandSuccess" />
        <p class="text-sm text-brandSuccess font-medium">{{ successMessage || 'Selection is valid' }}</p>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, nextTick, watch } from 'vue';
import UiDropdown from '../../../../components/ui/UiDropdown.vue';

interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  group?: string;
  font?: string;
  description?: string;
}

interface SelectGroup {
  label?: string;
  options: SelectOption[];
}

interface Props {
  id: string;
  modelValue: string | number | null | undefined;
  label?: string;
  options: SelectOption[];
  width?: string | number;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  multiple?: boolean; // Note: Multiple select is not supported in this custom implementation
  error?: string;
  helpText?: string;
  helpTextIcon?: boolean;
  successMessage?: string;
  leadingIcon?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  loading?: boolean;
  showSuccess?: boolean;
  showCheckIcon?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string | number | null | undefined): void;
  (e: 'focus', event: FocusEvent): void;
  (e: 'blur', event: FocusEvent): void;
  (e: 'change', value: string | number | null | undefined): void;
}

const props = withDefaults(defineProps<Props>(), {
  width: 'full', // Default width is full
  size: 'md',
  loading: false,
  showSuccess: false,
  multiple: false,
  helpTextIcon: false,
  showCheckIcon: false,
});

const emit = defineEmits<Emits>();

// Refs
const dropdownRef = ref<InstanceType<typeof UiDropdown> | null>(null);
const selectRef = ref<HTMLButtonElement | null>(null);
const optionsListRef = ref<HTMLUListElement | null>(null);
const focusedOptionIndex = ref(-1);

// Computed properties
const isOpen = computed(() => dropdownRef.value?.isOpen || false);

const widthClasses = computed(() => {
  if (!props.width) return 'w-full';
 
  // Handle CSS units
  const cssUnits = ['px', 'em', 'rem', 'vh', 'vw', 'vmin', 'vmax'];
  if (cssUnits.some(unit => props.width?.includes(unit))) {
    return `w-[${props.width}]`;
  }

  // Handle custom properties
  if (props.width.startsWith('--')) {
    return `w-(${props.width})`;
  }
 
  // Handle special widths
  return `w-${props.width}`;
});

const sizeClasses = computed(() => {
  const sizeMap = {
    xs: 'px-2 py-1.5 text-xs',
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-sm',
    lg: 'px-5 py-4 text-base'
  };
  return sizeMap[props.size];
});

const stateClasses = computed(() => {
  if (props.error) {
    return 'border-brandDanger bg-red-50 dark:bg-red-900/10 text-brandDanger focus:ring-brandDanger/20 focus:border-brandDanger';
  }
  if (props.showSuccess && props.modelValue && !props.error) {
    return 'border-brandSuccess bg-green-50 dark:bg-green-900/10 focus:ring-brandSuccess/20 focus:border-brandSuccess';
  }
  if (props.disabled) {
    return 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 cursor-not-allowed opacity-60';
  }
  return 'bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 focus:ring-brandPrimary/20 focus:border-brandPrimary';
});

const selectedClass = computed(() => 'bg-brandPrimary/10 text-brandPrimary dark:bg-brandPrimary/20');

const flatOptions = computed(() => props.options);

const selectedOption = computed(() => flatOptions.value.find(o => o.value === props.modelValue));

const hasGroups = computed(() => props.options.some(option => option.group));

const groupedOptions = computed((): SelectGroup[] => {
  if (!hasGroups.value) return [{ options: props.options }];
  const groups: { [key: string]: SelectOption[] } = {};
  props.options.forEach(option => {
    const groupName = option.group || 'ungrouped';
    if (!groups[groupName]) groups[groupName] = [];
    groups[groupName].push(option);
  });
  return Object.entries(groups).map(([label, options]) => ({
    label: label === 'ungrouped' ? undefined : label,
    options
  }));
});

// Methods
const openDropdown = () => {
  if (props.disabled || props.loading) return;
  dropdownRef.value?.open();
  focusedOptionIndex.value = Math.max(0, flatOptions.value.findIndex(o => o.value === props.modelValue));
};

const selectOption = (option: SelectOption, close: () => void) => {
  if (option.disabled) return;
  emit('update:modelValue', option.value);
  emit('change', option.value);
 
  
  close();
};

const isSelected = (option: SelectOption) => props.modelValue === option.value;

const focusOption = (index: number) => {
  focusedOptionIndex.value = index;
};

const isOptionFocused = (index: number) => focusedOptionIndex.value === index;

const focusNextOption = () => {
  if (!isOpen.value) openDropdown();
  let nextIndex = focusedOptionIndex.value + 1;
  while (nextIndex < flatOptions.value.length && flatOptions.value[nextIndex].disabled) {
    nextIndex++;
  }
  if (nextIndex < flatOptions.value.length) {
    focusedOptionIndex.value = nextIndex;
  }
};

const focusPreviousOption = () => {
  if (!isOpen.value) openDropdown();
  let prevIndex = focusedOptionIndex.value - 1;
  while (prevIndex >= 0 && flatOptions.value[prevIndex].disabled) {
    prevIndex--;
  }
  if (prevIndex >= 0) {
    focusedOptionIndex.value = prevIndex;
  }
};

const getFlatOptionIndex = (groupIndex: number, optionIndex: number): number => {
  let index = 0;
  for (let i = 0; i < groupIndex; i++) {
    index += groupedOptions.value[i].options.length;
  }
  return index + optionIndex;
};

// Lifecycle Hooks
watch(isOpen, (isNowOpen) => {
  if (isNowOpen) {
    nextTick(() => {
      optionsListRef.value?.focus();
      const selectedEl = optionsListRef.value?.querySelector(`#option-${focusedOptionIndex.value}`);
      selectedEl?.scrollIntoView({ block: 'nearest' });
    });
  } else {
    focusedOptionIndex.value = -1;
    selectRef.value?.focus();
  }
});

watch(focusedOptionIndex, (newIndex) => {
  if (newIndex > -1) {
    const optionEl = optionsListRef.value?.querySelector(`#option-${newIndex}`);
    optionEl?.scrollIntoView({ block: 'nearest' });
  }
});


// Expose methods for external control
defineExpose({
  focus: () => selectRef.value?.focus(),
  blur: () => selectRef.value?.blur(),
  selectRef
});
</script>

<style scoped>
/* Enhanced select animations and transitions */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease-in-out;
}
.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.error-slide-enter-active,
.error-slide-leave-active,
.success-slide-enter-active,
.success-slide-leave-active {
  transition: all 0.3s ease-out;
}

.error-slide-enter-from,
.success-slide-enter-from {
  opacity: 0;
  transform: translateY(-8px);
}

.error-slide-leave-to,
.success-slide-leave-to {
  opacity: 0;
  transform: translateY(-4px);
}

/* Custom select styling */
select {
  background-image: none; /* Remove default arrow */
}

/* Enhanced focus ring animation */
select:focus + .focus-ring {
  transform: scale(1.02);
  opacity: 1;
}

/* Custom option styling */
select option {
  padding: 0.5rem 0.75rem;
  background-color: white;
  color: inherit;
}

select option:checked {
  background-color: var(--color-brandPrimary);
  color: white;
}

select option:disabled {
  color: #9ca3af;
  background-color: #f3f4f6;
}

/* Dark mode option styling */
@media (prefers-color-scheme: dark) {
  select option {
    background-color: rgb(17, 24, 39);
    color: rgb(243, 244, 246);
  }

  select option:checked {
    background-color: var(--color-brandPrimary);
    color: white;
  }

  select option:disabled {
    color: rgb(107, 114, 128);
    background-color: rgb(31, 41, 55);
  }
}

/* Enhanced disabled state */
select:disabled {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(0, 0, 0, 0.05) 2px,
    rgba(0, 0, 0, 0.05) 4px
  );
}

/* Multiple select styling */
select[multiple] {
  padding: 0.5rem;
}

select[multiple] option {
  padding: 0.25rem 0.5rem;
  margin: 0.125rem 0;
  border-radius: 0.25rem;
}

/* Custom scrollbar for multiple select */
select[multiple]::-webkit-scrollbar {
  width: 8px;
}

select[multiple]::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

select[multiple]::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

select[multiple]::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* Enhanced validation states */
select:valid:not([multiple]) {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%2310b981'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
  background-position: right 2.5rem center;
  background-repeat: no-repeat;
  background-size: 1rem 1rem;
}

select:invalid:not([multiple]) {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23ef4444'%3e%3cpath fill-rule='evenodd' d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z' clip-rule='evenodd'/%3e%3c/svg%3e");
  background-position: right 2.5rem center;
  background-repeat: no-repeat;
  background-size: 1rem 1rem;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .error-slide-enter-active,
  .error-slide-leave-active,
  .success-slide-enter-active,
  .success-slide-leave-active,
  select {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  select {
    border-width: 2px;
  }

  select:focus {
    outline: 3px solid;
    outline-offset: 2px;
  }

  select option:checked {
    background-color: HighlightText;
    color: Highlight;
  }
}

/* Print styles */
@media print {
  select {
    border: 1px solid #000;
    background: white;
    box-shadow: none;
  }
}
</style>
<template>
  <div :class="['editor-sidebar', { opened }]">
   
  </div>
</template>
<script setup lang="ts">
interface Props{
 opened?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  opened: false
})
</script>
<style scoped>
@reference '~/assets/css/tailwind.css';
.editor-sidebar {
    @apply border-e border-gray-200 w-0 bg-gray-50  flex flex-col items-start p-0 shadow-md absolute left-0 top-0 h-full z-10 transition-all ease-in-out duration-300;
}
.editor-sidebar.opened {
  @apply w-[250px] px-4 py-2;
}
</style>

<template>
  <Teleport to="body">
    <div class="global-modals-container">
      <!-- Render all active modals from the modal stack -->
      <template v-for="modal in openModals" :key="modal.id">
        <div
          :class="[
            'modal-wrapper',
            `modal-${modal.config.animation || 'fade'}`,
            { 'modal-closing': modal.isClosing },
          ]"
          :style="{ zIndex: modal.zIndex }"
        >
          <!-- Modal backdrop -->
          <div class="modal-backdrop" @click="handleBackdropClick(modal)" />

          <!-- Modal content container -->
          <div
            :class="[
              'modal-container',
              modal.config.className,
              sizeClasses[modal.config.size || ComponentSize.MD],
              { 'modal-centered': modal.config.centered !== false },
            ]"
            :style="modalStyles(modal)"
            role="dialog"
            :aria-modal="true"
            :aria-labelledby="modal.config.title ? `modal-title-${modal.id}` : undefined"
          >
            <!-- Modal Header -->
            <div
              v-if="modal.config.title || modal.config.closable !== false"
              class="modal-header"
            >
            <div v-if="modal.config?.icon" class="header-icon">
             <Icon  :name="modal.config?.icon" size="calc(var(--spacing) * 5)" />
            </div>
           
              <h3
                v-if="modal.config.title"
                :id="`modal-title-${modal.id}`"
                class="modal-title"
              >
                {{ modal.config.title }}
              </h3>
              <button
                v-if="modal.config.closable !== false"
                @click="closeModal(modal)"
                class="modal-close-button"
                type="button"
                aria-label="Close modal"
              >
                <Icon name="material-symbols:close" size="calc(var(--spacing) * 5)" />
              </button>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
        
              <!-- Render custom component if provided -->
              <component
                :ref="(el: any) => setComponentRef(modal, el)"
                v-if="modal.config.component"
                :is="modal.config.component"
                v-bind="modal.config.props || {}"
                v-on="modal.config.events || {}"
                @close="closeModal(modal)"
              />

              <!-- Render HTML content if provided -->
              <div v-else-if="modal.config.content" v-html="modal.config.content" />

              <!-- Default slot content -->
              <slot v-else :name="modal.id" :modal="modal">
              
                <div class="text-gray-600">Modal content goes here...</div>
              </slot>
            </div>

            <!-- Modal Footer -->
            <div v-if="hasModalActions(modal)" class="modal-footer">
              <button
                v-if="modal.config.onCancel"
                @click="handleCancel(modal)"
                class="modal-button modal-button-secondary"
                type="button"
              >
                {{ modal.config.cancelText || "Cancel" }}
              </button>
              <button
                v-if="modal.config.onOk"
                @click="handleOk(modal)"
                class="modal-button modal-button-primary"
                type="button"
              >
                {{ modal.config.okText || "OK" }}
              </button>
            </div>
          </div>
        </div>
      </template>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { useModalManager, type ModalInstance, type ModalConfig, useModal } from '../app/shared/composables/ui/useModal'
import { ComponentSize } from '../app/shared/types'
import type { Component, Ref } from 'vue'
 
import { modalComponentStore } from '../stores/modalComponentStore'
import { useEventBus } from '../app/shared/composables/core/useEventBus'
import { onMounted, onUnmounted, watch } from 'vue'
import { useScrollLock } from '../app/shared/composables/ui/useScrollLock'

// Extend ModalConfig to include additional properties for rendering
interface ExtendedModalConfig extends ModalConfig {
  component?: Component
  props?: Record<string, any>
  content?: string
  cancelText?: string
  okText?: string
}

interface ExtendedModalInstance extends Omit<ModalInstance, 'config'> {
  config: ExtendedModalConfig
  componentRef?: Ref<any>
}

// Use the modal manager to get access to all open modals
const { openModals, topModal } = useModalManager()
const { on, off } = useEventBus()
const scrollLock = useScrollLock()

onMounted(() => {
  on('open-modal', (config: ModalConfig) => {
    const modal = useModal(config)
    modal.open()
  })
})

onUnmounted(() => {
  off('open-modal', (config: ModalConfig) => {
    const modal = useModal(config)
    modal.open()
  })
})

watch(topModal, (newTopModal, oldTopModal) => {
  if (newTopModal && !oldTopModal) {
    scrollLock.lock()
  } else if (!newTopModal && oldTopModal) {
    scrollLock.unlock()
  }
})

// Size classes for different modal sizes
const sizeClasses: Record<ComponentSize | 'full', string> = {
  [ComponentSize.XS]: 'modal-xs',
  [ComponentSize.SM]: 'modal-sm',
  [ComponentSize.MD]: 'modal-md',
  [ComponentSize.LG]: 'modal-lg',
  [ComponentSize.XL]: 'modal-xl',
  full: 'modal-full'
}

// Set component reference for enhanced access
const setComponentRef = (modal: ExtendedModalInstance, el: any) => {
 
  
  if (el && modal.id) {
    modalComponentStore.setComponentRef(modal.id, el)
  }
}

// Clean up component reference when modal closes
const cleanupComponentRef = (modalId: string) => {
  modalComponentStore.removeComponentRef(modalId)
}




// Handle backdrop clicks
const handleBackdropClick = (modal: ModalInstance) => {
  if (modal.config.maskClosable !== false) {
    closeModal(modal)
  }
}

// Close modal
const closeModal = (modal: ModalInstance) => {
  // Clean up component reference
  cleanupComponentRef(modal.id)

  if (modal.config.onClose) {
    modal.config.onClose()
  }
}

// Handle cancel button
const handleCancel = (modal: ModalInstance) => {
  if (modal.config.onCancel) {
    modal.config.onCancel()
  }
  // closeModal(modal)
}

// Handle OK button
const handleOk = (modal: ModalInstance) => {
  if (modal.config.onOk) {
    modal.config.onOk({ component: modalComponentStore.getComponentRef(modal.id) , modal })
    // If you need to pass the component, use: modalComponents.value[modal.id]
    // modal.config.onOk({ component: modalComponents.value[modal.id] })
  }
  // closeModal(modal)
}

// Check if modal has action buttons
const hasModalActions = (modal: ModalInstance) => {
  return !!(modal.config.onCancel || modal.config.onOk)
}

// Generate modal styles
const modalStyles = (modal: ModalInstance) => {
  const styles: Record<string, string> = {}

  if (modal.config.maxWidth) {
    styles.maxWidth = modal.config.maxWidth
  }

  if (modal.config.maxHeight) {
    styles.maxHeight = modal.config.maxHeight
  }

  return styles
}
</script>

<style scoped>
@reference "~/assets/css/tailwind.css";
.global-modals-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.modal-container {
  position: relative;
  background: white;
  border-radius: 0.25rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-height: 90vh;
  overflow: hidden;
  margin: 1rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
 
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid var(--color-gray-200);
  flex-shrink: 0;
  width: 100%;
  min-width: 250px;
}
.header-icon {
  @apply flex pe-2 h-full justify-center items-center;
 
}
.modal-title {
  flex-grow: 1;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0;
 
}

.modal-close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  background: none;
  color: var(--color-gray-400);
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.modal-close-button:hover {
  color: var(--color-gray-600);
  background-color: var(--color-gray-100);
}

.modal-close-button:focus {
  outline: 2px solid var(--color-blue-500);
  outline-offset: 2px;
}

/* Modal Body */
.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex-grow: 1;
      width: 100%;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  border-top: 1px solid var(--color-gray-200);
  flex-shrink: 0;
  width: 100%;
}

.modal-button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.modal-button-primary {
  background-color: var(--color-blue-600);
  color: white;
}

.modal-button-primary:hover {
  background-color: var(--color-blue-700);
}

.modal-button-primary:focus {
  outline: 2px solid var(--color-blue-500);
  outline-offset: 2px;
}

.modal-button-secondary {
  background-color: white;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.modal-button-secondary:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.modal-button-secondary:focus {
  outline: 2px solid var(--color-gray-500);
  outline-offset: 2px;
}

.modal-centered {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Modal sizes */
.modal-xs {
  max-width: 20rem;
}

.modal-sm {
  max-width: 24rem;
}

.modal-md {
  max-width: 32rem;
}

.modal-lg {
  max-width: 48rem;
}

.modal-xl {
  max-width: 64rem;
}

.modal-full {
  max-width: 95vw;
  max-height: 95vh;
  margin: 2.5vh 2.5vw;
  width: 100%;
  height: 100%;
}

/* Animation classes */
.modal-fade {
  transition: opacity 0.3s ease;
}

.modal-fade.modal-closing {
  opacity: 0;
}

.modal-slide {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal-slide.modal-closing {
  transform: translateY(-2rem);
  opacity: 0;
}

.modal-zoom {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal-zoom.modal-closing {
  transform: scale(0.95);
  opacity: 0;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .modal-container {
    background: var(--color-gray-800);
    color: var(--color-gray-100);
  }

  .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .modal-header {
    border-bottom-color: var(--color-gray-700);
  }

  .modal-title {
    color: var(--color-gray-100);
  }

  .modal-close-button {
    color: var(--color-gray-400);
  }

  .modal-close-button:hover {
    color: var(--color-gray-200);
    background-color: var(--color-gray-700);
  }

  .modal-footer {
    border-top-color: var(--color-gray-700);
  }

  .modal-button-secondary {
    background-color: var(--color-gray-700);
    color: var(--color-gray-200);
    border-color: var(--color-gray-600);
  }

  .modal-button-secondary:hover {
    background-color: var(--color-gray-600);
    border-color: var(--color-gray-500);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal-container {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .modal-full {
    margin: 0;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }
}

/* RTL support */
[dir="rtl"] .modal-container {
  text-align: right;
}

/* Accessibility improvements */
.modal-container:focus {
  outline: 2px solid var(--color-blue-500);
  outline-offset: 2px;
}

/* Smooth scrolling for modal content */
.modal-container {
  scroll-behavior: smooth;
}

/* Prevent body scroll when modal is open */
:global(body.modal-open) {
  overflow: hidden;
}
</style>

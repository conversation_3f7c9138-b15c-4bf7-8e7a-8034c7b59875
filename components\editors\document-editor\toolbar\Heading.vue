<template>
  <div class="heading-toolbar-container">
    <div class="heading-toolbar-row" :class="{ 'no-bottom-border': showRestHeadings }" ref="firstRowRef">
      <div v-for="head in firstHeadings" :key="head.value">
        <div
          :class="['heading-toolbar-item']"
          @click="$emit('select', head.value)"
        >
        
          <component
            :is="head.tag"
            :class="[fontSizeClass(head), 'scale-65 whitespace-nowrap']"
          >
            {{ head.label }}
          </component>

          <small class="text-xs text-gray-400 uppercase">{{ head.tag }}</small>
        </div>
      </div>
      <UiButton
        variant="flat"
        size="xs"
        class="p-2"
        @click="toggle"
      >
        <Icon
          :name="showRestHeadings ? 'heroicons:chevron-up' : 'heroicons:chevron-down'"
          size="calc(var(--spacing) * 3)"
        />
      </UiButton>
    </div>
    <Teleport to="body">
      <Transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div
        :style="style"
          :class="[
            'heading-toolbar-row absolute left-0 z-10 transition-all duration-300',
            { 'no-top-border': showRestHeadings },
          ]"
          v-show="showRestHeadings"
 
        >
          <div v-for="head in restHeadings" :key="head.value">
            <div class="heading-toolbar-item" @click="$emit('select', head.value)">
              <component
                :is="head.tag"
                :class="[fontSizeClass(head), 'scale-65 whitespace-nowrap']"
              >
                {{ head.label }}
              </component>

              <small class="text-xs text-gray-400 uppercase">{{ head.tag }}</small>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, useTemplateRef } from "vue";
import { onClickOutside } from '@vueuse/core'
interface Heading {
  label: string;
  value: number;
  tag: string;
}

interface Props {
  heads?: Heading[];
}

const props = withDefaults(defineProps<Props>(), {
  heads: () => [
    { label: "Normal", value: 0, tag: "p" },
    { label: "Heading 1", value: 1, tag: "h1" },
    { label: "Heading 2", value: 2, tag: "h2" },
    { label: "Heading 3", value: 3, tag: "h3" },
    { label: "Heading 4", value: 4, tag: "h4" },
    { label: "Heading 5", value: 5, tag: "h5" },
    { label: "Heading 6", value: 6, tag: "h6" },
    // { label: "Heading 7", value: 7, tag: "h7" },
  ],
});
const showRestHeadings = ref(false); // Initial state of the dropdown
const to = 4;
const firstHeadings = computed(() => props.heads.slice(0, to));
const restHeadings = computed(() => props.heads.slice(to));

const fontSizeClass = (head: Heading) => {
  switch (head.tag) {
    case "h1":
      return "text-3xl";
    case "h2":
      return "text-2xl";
    case "h3":
      return "text-xl";
    case "h4":
      return "text-lg";
    case "h5":
      return "text-base";
    case "h6":
      return "text-sm";
    case "h7":
      return "text-xs";
    default:
      return "text-base";
  }
};


const firstRowRef = useTemplateRef<HTMLElement>('firstRowRef');
const positionX = ref(0)
const width = ref(0)
const positionY = ref(0)
const style = computed(() => ({ top: positionY.value + 'px', left:  positionX.value + 'px', width: width.value + 'px'  }))
const calculatePosition = () => {
  if (firstRowRef.value) {
    const rect = firstRowRef.value.getBoundingClientRect();
    width.value = rect.width
    positionX.value = rect.x;
    positionY.value = rect.y + rect.height;
  }
};
const open = () => {

  if (showRestHeadings.value) return;
  showRestHeadings.value = true;
  calculatePosition();
 
};

const close = () => {
  showRestHeadings.value = false;
 
};

const toggle = () => {
  showRestHeadings.value ? close() : open();
 
};


onClickOutside(firstRowRef, close)
</script>
<style scoped>
@reference '~/assets/css/tailwind.css';
.heading-toolbar-container {
  @apply flex flex-col space-y-2 align-middle justify-center relative;
}
.heading-toolbar-row {
  @apply flex bg-gray-100 py-1.5 px-2 space-x-2 w-full border-1 border-gray-200 dark:border-gray-700;
}
.heading-toolbar-item {
  @apply flex flex-col py-2 px-4 bg-gray-50 rounded-xs items-center justify-center w-full;
  min-width: 100px;
  max-width: 100px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  min-height: 60px;
  max-height: 60px;
}

.no-bottom-border {
  @apply border-b-0;
}
.no-top-border {
  @apply border-t-0;
}

.heading-toolbar-item:hover {
  border: 1px solid #0072e6;
}
</style>

<template>
  <div class="home-toolbar-tab">
    <div class="home-toolbar-col">
      <div class="flex">
        <Button
          label="Undo (Ctrl+Z)"
          icon-size="md"
          icon="material-symbols:undo"
          command="undo"
          @click="executeCommand"
        />
        <Button
          label="Redo (Ctrl+Y)"
          icon-size="md"
          icon="material-symbols:redo"
          command="redo"
          @click="executeCommand"
        />
      </div>
      <div class="flex">
        <Button
          label="Clear Formatting"
          icon-size="md"
          icon="material-symbols:format-clear"
          command="clearFormatting"
          @click="executeCommand"
        />
        <Button
          label="Select All (Ctrl+A)"
          icon-size="md"
          icon="icon-park-outline:union-selection"
          command="selectAll"
          @click="executeCommand"
        />
      </div>
    </div>

    <Divider />
    <div class="home-toolbar-col">
      <div class="flex space-x-2">
        <UiSelect
          id="font-select"
          v-model="selectedFont"
          label="Font"
          size="xs"
          width="40"
          :options="fontOptions"
          @change="handleFontChange"
        />
        <UiSelect
          id="size-select"
          v-model="selectedSize"
          label="Size"
          size="xs"
          width="24"
          :options="sizeOptions"
          @change="handleSizeChange"
        />
      </div>
      <div class="flex">
        <Button
          label="Bold (Ctrl+B)"
          icon-size="sm"
          icon="heroicons:bold"
          command="bold"
          @click="executeCommand"
        />
        <Button
          label="Italic (Ctrl+I)"
          icon-size="sm"
          icon="heroicons:italic"
          command="italic"
          @click="executeCommand"
        />
        <Button
          label="Underline (Ctrl+U)"
          icon-size="sm"
          icon="heroicons:underline"
          command="underline"
          @click="executeCommand"
        />
        <Button
          label="Strikethrough (Ctrl+Shift+X)"
          icon-size="sm"
          icon="heroicons:strikethrough"
          command="strikethrough"
          @click="executeCommand"
        />
        <Button
          label="Superscript (Ctrl+Shift+S)"
          icon-size="sm"
          icon="teenyicons:superscript-outline"
          command="superscript"
          @click="executeCommand"
        />
        <Button
          label="Subscript (Ctrl+Shift+D)"
          icon-size="sm"
          icon="teenyicons:subscript-outline"
          command="subscript"
          @click="executeCommand"
        />
      </div>
    </div>
    <Divider />
    <div class="home-toolbar-col">
      <div class="flex align-middle justify-center">
        <DropDownButton
          label="Numbered List (Ctrl+Shift+7)"
          icon="material-symbols:format-list-numbered"
          icon-size="md"
          width="318px"
          command="orderedList"
          @click="executeCommand"
          @select="handleListSelection"
        >
          <template #dropdown="{ close, select }">
            <div class="p-2">
              <div class="grid grid-cols-4 gap-1">
                <template v-for="listType in orderList" :key="listType.value">
                  <Button
                    :label="listType.label"
                    @click="
             
                      select({ command: 'orderedList', value: listType.value });
                      close();
                    "
                  >
                    <IconList
                      variant="text"
                      :letters="listType.letters"
                      :rtl="listType.rtl"
                    />
                  </Button>
                </template>
              </div>
            </div>
          </template>
        </DropDownButton>
        <DropDownButton
          label="Bullet List (Ctrl+Shift+8)"
          icon="material-symbols:format-list-bulleted"
          icon-size="md"
          width="318px"
          command="bulletList"
          @click="executeCommand"
          @select="handleListSelection"
        >
          <template #dropdown="{ close, select }">
            <div class="p-2">
              <div class="grid grid-cols-4 gap-1">
                <template v-for="listType in bulletList" :key="listType.value">
                  <Button
                    :label="listType.label"
                    @click="
                      select({ command: 'bulletList', value: listType.value });
                      close();
                    "
                  >
                    <IconList :variant="listType.variant" :fill="listType.fill" />
                  </Button>
                </template>
              </div>
            </div>
          </template>
        </DropDownButton>

        <Button
          label="Outdent (Ctrl+Shift+Left)"
          icon-size="md"
          icon="material-symbols:format-indent-decrease"
          command="outdent"
          @click="executeCommand"
        />
        <Button
          label="Indent (Ctrl+Shift+Right)"
          icon-size="md"
          icon="material-symbols:format-indent-increase"
          command="indent"
          @click="executeCommand"
        />
        <!-- <Button
          label="Line Height"
          icon-size="md"
          icon="material-symbols:line-weight"
          command="lineHeight"
          @click="executeCommand"
        /> -->
        <DropDownButton
          label="Line Height"
          icon="material-symbols:line-weight"
          :disabled="!hasSelection"
          :splited="false"
          icon-size="md"
          command="lineHeight"
          @click="executeCommand"
          @select="handlelineHeightSelection"
        >
          <template #dropdown="{ close, select }">
            <div class="flex flex-col space-y-1">
              <template v-for="lineHeight in lineHeightList" :key="lineHeight.value">
                <UiButton
                  full-width
                  :label="lineHeight.label"
                  variant="flat"
                  color="gray"
                  @click="select({ command: 'lineHeight', value: lineHeight.value }); close()"
                >
                  {{ lineHeight.label }}
                </UiButton>
              </template>
            </div>
          </template>
        </DropDownButton>
        <Button
          label="Quote"
          icon-size="md"
          icon="material-symbols:format-quote"
          command="quote"
          @click="executeCommand"
        />
      </div>
      <div class="flex align-middle justify-center">
        <Button
          label="Align Left"
          icon="heroicons:bars-3-bottom-left"
          icon-size="md"
          command="left"
          @click="setTextAlignment"
        />
        <Button
          label="Align Center"
          icon="heroicons:bars-3"
          icon-size="md"
          command="center"
          @click="setTextAlignment"
        />
        <Button
          label="Align Right"
          icon="heroicons:bars-3-bottom-right"
          icon-size="md"
          command="right"
          @click="setTextAlignment"
        />
        <Button
          label="Justify (Ctrl+J)"
          icon-size="md"
          icon="heroicons:bars-4"
          command="justify"
          @click="setTextAlignment"
        />
        <Button
          label="Distribute"
          icon-size="md"
          icon="material-symbols-light:align-justify-space-even-rounded"
          command="distribute"
          @click="executeCommand"
        />
      </div>
    </div>
    <Divider />
    <Heading @select="handleHeadingSelection" />
    <Divider />
    <div class="home-toolbar-col">
      <div class="flex">
        <Button
          label="Import Word"
          show-label
          icon-size="xl"
          icon="bi:file-earmark-word"
          command="importWord"
        />
        <Button
          label="Search"
          show-label
          icon-size="xl"
          icon="garden:document-search-stroke-12"
          command="search"
        />
      </div>
    </div>
    <Divider />
    <div class="home-toolbar-col">
      <div class="flex">
        <Button
          label="Print"
          show-label
          icon-size="xl"
          icon="heroicons:printer"
          command="print"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, getCurrentInstance, onUnmounted } from "vue"; // Import ref from Vue
import UiSelect from "../../../../app/shared/components/ui/UiSelect.vue";
import Button from "./Button.vue";
import Divider from "./Divider.vue";
import Heading from "./Heading.vue";
import DropDownButton from "./DropDownButton.vue";
import { useDocEditor } from "~/app/shared/composables/core/editors/useDocEditor";
import IconList from "../../../icons/IconList.vue";

const { executeCommand, hasSelection } = useDocEditor();

const selectedFont = ref("Calibri");
const selectedSize = ref("11");
const fontOptions = ref([
  { label: "Calibri", value: "Calibri", font: "var(--font-family-calibri)" },
  {
    label: "Times New Roman",
    value: "Times New Roman",
    font: "var(--font-family-times-new-roman)",
  },
  { label: "Arial", value: "Arial", font: "var(--font-family-arial)" },
  { label: "Verdana", value: "Verdana", font: "var(--font-family-verdana)" },
  { label: "Helvetica", value: "Helvetica", font: "var(--font-family-helvetica)" },
  { label: "Tahoma", value: "Tahoma", font: "var(--font-family-tahoma)" },
  { label: "Georgia", value: "Georgia", font: "var(--font-family-georgia)" },
  { label: "Garamond", value: "Garamond", font: "var(--font-family-garamond)" },
  {
    label: "Comic Sans MS",
    value: "Comic Sans MS",
    font: "var(--font-family-comic-sans-ms)",
  },
  { label: "Impact", value: "Impact", font: "var(--font-family-impact)" },
  { label: "Courier New", value: "Courier New", font: "var(--font-family-courier-new)" },
  {
    label: "Lucida Console",
    value: "Lucida Console",
    font: "var(--font-family-lucida-console)",
  },
  { label: "Symbol", value: "Symbol", font: "var(--font-family-symbol)" },
  { label: "Webdings", value: "Webdings", font: "var(--font-family-webdings)" },
]); // Added missing closing bracket
const sizeOptions = ref([
  { label: "8", value: "8" },
  { label: "9", value: "9" },
  { label: "10", value: "10" },
  { label: "11", value: "11" },
  { label: "12", value: "12" },
  { label: "14", value: "14" },
  { label: "16", value: "16" },
  { label: "18", value: "18" },
  { label: "20", value: "20" },
  { label: "24", value: "24" },
  { label: "28", value: "28" },
  { label: "32", value: "32" },
  { label: "36", value: "36" },
  { label: "48", value: "48" },
  { label: "72", value: "72" },
  { label: "96", value: "96" },
]); // Added missing closing bracket

const bulletList = computed(() => {
  return [
    {
      label: "Filled Circle",
      variant: "disc" as const,
      fill: true,
      value: "list-bullet-filled-circle",
    },
    {
      label: "Opened Circle",
      variant: "disc" as const,
      fill: false,
      value: "list-bullet-open-circle",
    },
    {
      label: "Filled Square",
      variant: "rect" as const,
      fill: true,
      value: "list-bullet-filled-rect",
    },
    {
      label: "Opened Square",
      variant: "rect" as const,
      fill: false,
      value: "list-bullet-open-rect",
    },
  ];
}); // Added missing closing bracket

const orderList = computed(() => {
  return [
    { label: "Numbers", letters: ["1", "2", "3"], value: "list-ordered-numbers" },
    {
      label: "Numbers with leading zeros",
      letters: ["01", "02", "03"],
      value: "list-ordered-leading-zero",
    },
    {
      label: "Lowercase letters",
      letters: ["a", "b", "c"],
      value: "list-ordered-alpha-lower",
    },
    {
      label: "Uppercase letters",
      letters: ["A", "B", "C"],
      value: "list-ordered-alpha-upper",
    },
    {
      label: "Lowercase Roman numerals",
      letters: ["i", "ii", "iii"],
      value: "list-ordered-roman-lower",
    },
    {
      label: "Uppercase Roman numerals",
      letters: ["I", "II", "III"],
      value: "list-ordered-roman-upper",
    },
    {
      label: "Arabic alphabetic",
      letters: ["أ", "ب", "ج"],
      value: "list-ordered-arabic",
      rtl: true,
    },
    {
      label: "Hebrew alphabetic",
      letters: ["א", "ב", "ג"],
      value: "list-ordered-hebrew",
      rtl: true,
    },
  ];
});

const handleFontChange = () => {
  executeCommand("fontFamily", selectedFont.value);
};

const handleSizeChange = () => {
  executeCommand("fontSize", selectedSize.value);
};

const setTextAlignment = (alignment: string) => {
  executeCommand("textAlign", alignment);
};
const handleHeadingSelection = (heading: string) => {
 
  executeCommand("heading", heading);
};
const handleListSelection = (list: string) => {
 
  executeCommand("list", list);
};

const handlelineHeightSelection = (lineHeight) => {
   
  executeCommand("lineHeight", lineHeight);
};
const lineHeightList = computed(() => {
  return [
    { label: "Single", value: 1 },
    { label: "1.5 Line Spacing (Default)", value: 1.5 },
    { label: "Double", value: 2 },
    { label: "2.5 Line Spacing", value: 2.5 },
    { label: "Triple", value: 3 },
  ];
});

 


const emit = defineEmits(["mounted", "unmounted"]);

onMounted(() => {
  emit("mounted", getCurrentInstance());
});

onUnmounted(() => {
  emit("unmounted", getCurrentInstance());
});
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.home-toolbar-tab {
  @apply flex align-middle justify-center;
}
.home-toolbar-col {
  @apply flex flex-col space-y-2 align-middle justify-center;
}
</style>

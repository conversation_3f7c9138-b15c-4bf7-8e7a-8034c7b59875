<template>
  <div class="relative" >

    <!-- Enhanced Label -->
    <label
      v-if="label"
      :for="id"
      :class="[
        'block text-sm font-semibold transition-colors duration-200 mb-2',
        props.errorMessage ? 'text-brandDanger' : 'text-gray-700 dark:text-gray-300',
        required ? 'after:content-[\'*\'] after:text-brandDanger after:ml-1' : ''
       ]"
    >
      {{ label }}
    </label>

    <!-- Input Container -->
    <div class="relative group">
      <!-- Leading Icon -->
      <div v-if="leadingIcon" class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Icon
          :name="leadingIcon"
          size="calc(var(--spacing) * 5)"
          :class="[
            ' transition-colors duration-200',
            props.errorMessage ? 'text-brandDanger' : 'text-gray-400 group-focus-within:text-brandPrimary'
           ]"
        />
      </div>
      <div v-if="leadingText" class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        {{ leadingText }}
      </div>
      <!-- Enhanced Input -->
      <input
        :id="id"
        ref="inputRef"
        :type="computedType"
        v-model="value"
        :placeholder="placeholder"
        :required="required"
        :disabled="disabled"
        :readonly="readonly"
        :autocomplete="autocomplete"
        :maxlength="maxlength"
        :minlength="minlength"
        :pattern="pattern"
        :class="[
          'block w-full transition-all duration-200 ease-in-out',
          'border rounded-md leading-5 placeholder-gray-400',
          'focus:outline-none focus:ring-2 focus:ring-offset-0',
          'sm:text-sm font-medium',
          sizeClasses,
          stateClasses,
          leadingIcon ? 'pl-10' : '',
          trailingIcon || showPasswordToggle || clearable ? 'pr-10' : '',
          attrs.class || ''
        ]"
        v-bind="filteredAttrs"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
        @keydown="handleKeydown"
      />
      <div v-if="trailingText" class="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
        {{trailingText}}
      </div>
      <!-- Trailing Icons Container -->
      <div v-if="trailingIcon || showPasswordToggle || clearable || loading" class="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
        <!-- Loading Spinner -->
        <UiSpinner v-if="loading" size="xs" class="text-brandPrimary" />

        <!-- Clear Button -->
        <button
          v-else-if="clearable && value && !disabled && !readonly"
          @click="clearInput"
          type="button"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          aria-label="Clear input"
        >
          <Icon name="material-symbols:close" class="h-4 w-4" />
        </button>

        <!-- Password Toggle -->
        <button
          v-else-if="showPasswordToggle"
          @click="togglePasswordVisibility"
          type="button"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          :aria-label="isPasswordVisible ? 'Hide password' : 'Show password'"
        >
          <Icon :name="isPasswordVisible ? 'material-symbols:visibility-off' : 'material-symbols:visibility'" class="h-4 w-4" />
        </button>

        <!-- Trailing Icon -->
        <Icon
          v-else-if="trailingIcon"
          :name="trailingIcon"
          size="calc(var(--spacing) * 5)"
          :class="[
            ' transition-colors duration-200',
            props.errorMessage ? 'text-brandDanger' : 'text-gray-400'
           ]"
        />
      </div>

      <!-- Focus Ring Animation -->
      <div
        :class="[
          'absolute inset-0 rounded-xl pointer-events-none transition-all duration-200',
          'ring-0 ring-brandPrimary/20'
        ]"
      />
    </div>

    <!-- Character Count -->
    <div v-if="maxlength && showCharCount" class="mt-1 text-right">
      <span :class="[
        'text-xs transition-colors duration-200',
        (value?.toString().length || 0) > maxlength * 0.8 ? 'text-brandWarning-600' : 'text-gray-500',
        (value?.toString().length || 0) >= maxlength ? 'text-brandDanger' : ''
      ]">
        {{ value?.toString().length || 0 }}/{{ maxlength }}
      </span>
    </div>

    <!-- Enhanced Error Message -->
    <Transition name="error-slide">
      <div v-if="props.errorMessage" class="mt-2 flex items-start space-x-2">
        <Icon name="material-symbols:error" size="calc(var(--spacing) * 4)" class=" text-brandDanger flex-shrink-0 mt-0.5" />
        <p class="text-sm text-brandDanger font-medium">{{ props.errorMessage }}</p>
      </div>
    </Transition>

    <!-- Enhanced Help Text -->
    <p v-if="helpText && !props.errorMessage" class="mt-2 text-sm text-gray-500 dark:text-gray-400 flex items-start space-x-2">
      <Icon v-if="helpTextIcon" name="material-symbols:info" size="calc(var(--spacing) * 4)" class=" flex-shrink-0 mt-0.5" />
      <span class="text-xs">{{ helpText }}</span>
    </p>

    <!-- Success State -->
    <Transition name="success-slide">
      <div v-if="showSuccess && !props.errorMessage && value" class="mt-2 flex items-center space-x-2">
        <Icon name="material-symbols:check-circle" size="calc(var(--spacing) * 4)" class=" text-brandSuccess" />
        <p class="text-sm text-brandSuccess font-medium">{{ successMessage || 'Input is valid' }}</p>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed, useAttrs, defineEmits, ref, nextTick } from 'vue';

interface Props {
  id: string;
  modelValue?: string | number | null;
  errorMessage?: string;
  label?: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  helpText?: string;
  helpTextIcon?: boolean;
  successMessage?: string;
  leadingIcon?: string;
  trailingIcon?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  clearable?: boolean;
  loading?: boolean;
  showCharCount?: boolean;
  showSuccess?: boolean;
  autocomplete?: string;
  maxlength?: number;
  minlength?: number;
  pattern?: string;
  name?: string; // Keep name for accessibility and form submission
  leadingText?: string;
  trailingText?: string;
}

interface Emits {
  (e: 'update:modelValue', payload: string | number | null | undefined): void;
  (e: 'focus', event: FocusEvent): void;
  (e: 'blur', event: FocusEvent): void;
  (e: 'input', event: Event): void;
  (e: 'keydown', event: KeyboardEvent): void;
  (e: 'clear'): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'md',
  variant: 'default',
  clearable: false,
  loading: false,
  showCharCount: false,
  showSuccess: false,
  readonly: false,
  helpTextIcon: false,
});

const emit = defineEmits<Emits>();

const attrs = useAttrs();

// Refs
const inputRef = ref<HTMLInputElement | null>(null);
const isFocused = ref(false);
const isPasswordVisible = ref(false);

// Filter out class from attrs to handle it separately
const filteredAttrs = computed(() => {
  const { class: _, ...rest } = attrs;
  return rest;
});

// The `value` is now managed directly via v-model (props.modelValue)
const value = computed({
  get: () => {
    if (props.type === 'number') {
      return props.modelValue ?? '';
    }
    return props.modelValue || '';
  },
  set: (newValue) => {
    if (props.type === 'number') {
      emit('update:modelValue', newValue === '' ? null : Number(newValue));
    } else {
      emit('update:modelValue', newValue);
    }
  }
});

// Enhanced computed properties
const sizeClasses = computed(() => {
  const sizeMap = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-sm',
    lg: 'px-5 py-4 text-base'
  };
  return sizeMap[props.size];
});

const stateClasses = computed(() => {
  if (props.errorMessage) {
    return 'border-brandDanger bg-red-50 dark:bg-red-900/10 text-brandDanger focus:ring-brandDanger/20 focus:border-brandDanger';
  }

  if (props.showSuccess && value.value && !props.errorMessage) {
    return 'border-brandSuccess bg-green-50 dark:bg-green-900/10 focus:ring-brandSuccess/20 focus:border-brandSuccess';
  }

  if (props.disabled) {
    return 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 cursor-not-allowed opacity-60';
  }

  if (props.readonly) {
    return 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 cursor-default';
  }

  return 'bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 focus:ring-brandPrimary/20 focus:border-brandPrimary';
});

const computedType = computed(() => {
  if (props.type === 'password') {
    return isPasswordVisible.value ? 'text' : 'password';
  }
  return props.type;
});

const showPasswordToggle = computed(() => {
  return props.type === 'password';
});

// Event handlers
const handleFocus = (event: FocusEvent) => {
  isFocused.value = true;
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false;
  emit('blur', event);
};

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  value.value = target.value;
  emit('input', event);
};

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event);

  // Clear on Escape
  if (event.key === 'Escape' && props.clearable) {
    clearInput();
  }
};

const clearInput = () => {
  value.value = '';
  emit('clear');
  nextTick(() => {
    inputRef.value?.focus();
  });
};

const togglePasswordVisibility = () => {
  isPasswordVisible.value = !isPasswordVisible.value;
};

// Removed watchers that were syncing vee-validate state

// Expose methods for external control
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  clear: clearInput,
  inputRef
});
</script>

<style scoped>
/* Enhanced input animations and transitions */
.error-slide-enter-active,
.error-slide-leave-active,
.success-slide-enter-active,
.success-slide-leave-active {
  transition: all 0.3s ease-out;
}

.error-slide-enter-from,
.success-slide-enter-from {
  opacity: 0;
  transform: translateY(-8px);
}

.error-slide-leave-to,
.success-slide-leave-to {
  opacity: 0;
  transform: translateY(-4px);
}

/* Enhanced focus ring animation */
input:focus + .focus-ring {
  transform: scale(1.02);
  opacity: 1;
}

/* Smooth placeholder animation */
input::placeholder {
  transition: color 0.2s ease-in-out;
}

input:focus::placeholder {
  color: transparent;
}

/* Enhanced disabled state */
input:disabled {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(0, 0, 0, 0.05) 2px,
    rgba(0, 0, 0, 0.05) 4px
  );
}

/* Custom scrollbar for long inputs */
input[type="text"]::-webkit-scrollbar {
  height: 4px;
}

input[type="text"]::-webkit-scrollbar-track {
  background: transparent;
}

input[type="text"]::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

input[type="text"]::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* Enhanced autofill styling */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px white inset;
  -webkit-text-fill-color: inherit;
  transition: background-color 5000s ease-in-out 0s;
}

/* Dark mode autofill */
@media (prefers-color-scheme: dark) {
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px rgb(17, 24, 39) inset;
  }
}

/* Enhanced validation states */
/* input:valid:not(:placeholder-shown) {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%2310b981'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem 1rem;
}

input:invalid:not(:placeholder-shown) {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23ef4444'%3e%3cpath fill-rule='evenodd' d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z' clip-rule='evenodd'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem 1rem;
} */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .error-slide-enter-active,
  .error-slide-leave-active,
  .success-slide-enter-active,
  .success-slide-leave-active,
  input,
  input::placeholder {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  input {
    border-width: 2px;
  }

  input:focus {
    outline: 3px solid;
    outline-offset: 2px;
  }
}

/* Print styles */
@media print {
  input {
    border: 1px solid #000;
    background: white;
    box-shadow: none;
  }
}
</style>
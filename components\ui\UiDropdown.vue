<template>
  <div class="relative" ref="containerRef">
    <slot name="trigger" :toggle="toggle" :isOpen="isOpen"></slot>
    <Teleport to="body">
      <Transition name="dropdown">
        <div
          v-if="isOpen"
          ref="contentRef"
          class="absolute z-[1000]"
          :style="style"
          :class="[
            widthClass,
            openOnTop ? 'bottom-full mb-1' : 'top-full mt-1',
            'rounded-xs shadow-lg bg-white ring-1 ring-gray-300 ring-opacity-5 dark:bg-gray-800 dark:ring-gray-600',
          ]"
        >
          <slot name="content" :close="close"></slot>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, useTemplateRef } from "vue";
import { onClickOutside } from '@vueuse/core'
const props = defineProps({
  width: {
    type: String,
    default: "full",
  },
});

const widthClass = computed(() => {
 
  
  if (!props.width) return "w-64";

  if (props.width == 'full') {
    return `w-[${containerRef.value?.clientWidth}px]`;
    
  }
  // Handle CSS units
  const cssUnits = [
    "px",
    "em",
    "rem",
    "vh",
    "vw",
    "vmin",
    "vmax",
    "cm",
    "mm",
    "in",
    "pt",
    "pc",
    "ex",
    "ch",
    "q",
    "deg",
    "rad",
    "grad",
    "turn",
    "s",
    "ms",
    "Hz",
    "kHz",
    "dpi",
    "dpcm",
    "dppx",
    "vmin",
    "vmax",
    "vmin",
    "vmax",
  ];
  if (cssUnits.some((unit) => props.width?.includes(unit))) {
    return `w-[${props.width}]`;
  }

  // Handle custom properties
  if (props.width.startsWith("--")) {
    return `w-(${props.width})`;
  }

  // Handle special widths
  return `w-${props.width}`;
});

// const containerRef = ref<HTMLDivElement | null>(null);
const containerRef = useTemplateRef<HTMLElement>('containerRef')
const contentRef = useTemplateRef<HTMLElement>('contentRef')

 
const isOpen = ref(false);
const openOnTop = ref(false);

const positionX = ref(0)
const positionY = ref(0)
const style = computed(() => {
  const styleObj: Record<string, string | undefined> = {
    top: positionY.value + 'px',
    left: positionX.value + 'px',
  };
  if (props.width == 'full') {
    styleObj.width = `${containerRef.value?.clientWidth}px`;
  }
  return styleObj;
});
const calculatePosition = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect();
    positionX.value = rect.x;
    positionY.value = rect.y + rect.height;


    const spaceBelow = window.innerHeight - rect.bottom;
    const contentHeight = contentRef.value?.offsetHeight || 240; // Default height
    openOnTop.value = spaceBelow < contentHeight && rect.top > contentHeight;
   
    
  }
};

const emit = defineEmits(["open", "close", "toggle"]);

const open = () => {
  if (isOpen.value) return;
  isOpen.value = true;
  calculatePosition();
  emit("open", true);
};

const close = () => {
  isOpen.value = false;
  emit("close", false);
};

const toggle = () => {
  isOpen.value ? close() : open();
  emit("toggle", !isOpen.value);
};
onClickOutside(containerRef, close)


onMounted(() => {
  window.addEventListener("scroll", calculatePosition, true);
  window.addEventListener("resize", calculatePosition);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", calculatePosition, true);
  window.removeEventListener("resize", calculatePosition);
});

defineExpose({
  open,
  close,
  toggle,
  isOpen,
});
</script>

<style scoped>
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease-in-out;
}
.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scale(0.95);
}
</style>

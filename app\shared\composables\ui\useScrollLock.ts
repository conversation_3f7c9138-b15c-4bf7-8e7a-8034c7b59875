import { ref, onMounted, onUnmounted, watch } from 'vue';
import type { Ref } from 'vue';

export function useScrollLock(target: Ref<HTMLElement | Document> = ref(document.documentElement)) {
  const isLocked = ref(false);
  let originalOverflow = '';
  let originalPaddingRight = '';

  const lock = () => {
    if (isLocked.value || !target.value) return;

    const element = target.value === document.documentElement ? document.body : target.value as HTMLElement;
    
    originalOverflow = element.style.overflow;
    originalPaddingRight = element.style.paddingRight;

    // Calculate scrollbar width
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

    element.style.overflow = 'hidden';
    if (scrollbarWidth > 0) {
      element.style.paddingRight = `${scrollbarWidth}px`;
    }
    
    isLocked.value = true;
  };

  const unlock = () => {
    if (!isLocked.value || !target.value) return;

    const element = target.value === document.documentElement ? document.body : target.value as HTMLElement;

    element.style.overflow = originalOverflow;
    element.style.paddingRight = originalPaddingRight;
    
    isLocked.value = false;
  };

  watch(target, (newTarget, oldTarget) => {
    if (isLocked.value) {
      // If locked and target changes, unlock old target and lock new one
      if (oldTarget) {
        const oldElement = oldTarget === document.documentElement ? document.body : oldTarget as HTMLElement;
        oldElement.style.overflow = originalOverflow; // Assuming originalOverflow was for oldTarget
        oldElement.style.paddingRight = originalPaddingRight; // Assuming originalPaddingRight was for oldTarget
      }
      if (newTarget) {
        lock(); // Re-lock with the new target
      } else {
        isLocked.value = false; // No target to lock
      }
    }
  });
  

  return {
    isLocked,
    lock,
    unlock,
  };
}
import { Node, mergeAttributes } from '@tiptap/core'
import type { PageContentAttributes } from './types'

export const PageContent = Node.create({
  name: 'pageContent',
  
  group: 'block',
  content: 'block+',
  isolating: true,
  draggable: false,
  selectable: false,

  addAttributes() {
    return {
      pageNumber: {
        default: 1,
        parseHTML: element => parseInt(element.getAttribute('data-page-number') || '1'),
        renderHTML: attributes => ({
          'data-page-number': attributes.pageNumber,
        }),
      },
      overflow: {
        default: false,
        parseHTML: element => element.getAttribute('data-overflow') === 'true',
        renderHTML: attributes => {
          if (attributes.overflow) {
            return { 'data-overflow': 'true' }
          }
          return {}
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="page-content"]',
        priority: 100,
      },
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    const attrs = node.attrs as PageContentAttributes
    
    return [
      'div',
      mergeAttributes(HTMLAttributes, {
        'data-type': 'page-content',
        'class': 'tiptap-page-content',
        'style': `
          flex: 1;
          min-height: var(--content-height, 600px);
          max-height: var(--content-height, 600px);
          overflow: hidden;
          padding: var(--page-margin-top, 20px) var(--page-margin-right, 20px) var(--page-margin-bottom, 20px) var(--page-margin-left, 20px);
          box-sizing: border-box;
          position: relative;
        `,
      }),
      0,
    ]
  },

  addNodeView() {
    return ({ node, HTMLAttributes, getPos, editor }) => {
      const dom = document.createElement('div')
      const contentDOM = document.createElement('div')
      
      // Set up the container
      Object.assign(dom, {
        ...HTMLAttributes,
        'data-type': 'page-content',
        className: 'tiptap-page-content',
      })
      
      // Apply styles
      dom.style.cssText = `
        flex: 1;
        min-height: var(--content-height, 600px);
        max-height: var(--content-height, 600px);
        overflow: hidden;
        padding: var(--page-margin-top, 20px) var(--page-margin-right, 20px) var(--page-margin-bottom, 20px) var(--page-margin-left, 20px);
        box-sizing: border-box;
        position: relative;
      `
      
      // Set up content container
      contentDOM.style.cssText = `
        height: 100%;
        overflow: hidden;
      `
      
      dom.appendChild(contentDOM)

      // Overflow detection and auto page break
      const checkOverflow = () => {
        if (typeof getPos !== 'function') return
        
        const pos = getPos()
        if (pos === undefined) return

        const isOverflowing = contentDOM.scrollHeight > contentDOM.clientHeight
        const currentOverflow = node.attrs.overflow || false

        if (isOverflowing !== currentOverflow) {
          // Update overflow attribute
          const tr = editor.state.tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            overflow: isOverflowing,
          })
          
          if (isOverflowing) {
            // Add visual indicator for overflow
            dom.classList.add('tiptap-page-content-overflow')
            
            // Optionally trigger auto page break
            if (editor.storage.tiptapPages?.autoPageBreak) {
              // Find the last complete element that fits
              const children = Array.from(contentDOM.children)
              let lastFittingIndex = -1
              
              for (let i = 0; i < children.length; i++) {
                const child = children[i] as HTMLElement
                const rect = child.getBoundingClientRect()
                const containerRect = contentDOM.getBoundingClientRect()
                
                if (rect.bottom <= containerRect.bottom) {
                  lastFittingIndex = i
                } else {
                  break
                }
              }
              
              if (lastFittingIndex >= 0 && lastFittingIndex < children.length - 1) {
                // Insert page break after the last fitting element
                setTimeout(() => {
                  editor.commands.insertPageBreak()
                }, 100)
              }
            }
          } else {
            dom.classList.remove('tiptap-page-content-overflow')
          }
          
          editor.view.dispatch(tr)
        }
      }

      // Use ResizeObserver for better performance
      let resizeObserver: ResizeObserver | null = null
      if (typeof ResizeObserver !== 'undefined') {
        resizeObserver = new ResizeObserver(() => {
          requestAnimationFrame(checkOverflow)
        })
        resizeObserver.observe(contentDOM)
      }

      // Fallback to MutationObserver
      const mutationObserver = new MutationObserver(() => {
        requestAnimationFrame(checkOverflow)
      })
      
      mutationObserver.observe(contentDOM, {
        childList: true,
        subtree: true,
        characterData: true,
      })

      return {
        dom,
        contentDOM,
        destroy() {
          if (resizeObserver) {
            resizeObserver.disconnect()
          }
          mutationObserver.disconnect()
        },
      }
    }
  },
})

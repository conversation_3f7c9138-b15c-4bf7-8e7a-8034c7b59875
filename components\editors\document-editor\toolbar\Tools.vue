<template>
  <div class="tools-toolbar-tab">
    <div class="tools-toolbar-col">
      <div class="flex">
     <ui-alert title="placeholder" class="w-[89vw]" icon="i-heroicon:photo" />
        
      </div>
    </div>
    <Divider />
 
  </div>
</template>
<script setup lang="ts">
import Button from "./Button.vue"; // Import Button component
import Divider from "./Divider.vue"; // Import Divider component
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.tools-toolbar-tab {
  @apply flex align-middle justify-center;
}
.tools-toolbar-col {
  @apply flex flex-col space-y-2 align-middle justify-center;
}
</style>

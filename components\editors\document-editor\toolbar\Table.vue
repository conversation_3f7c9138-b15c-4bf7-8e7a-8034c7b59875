<template>
  <div class="table-toolbar-tab">
    <div class="table-toolbar-col">
      <div class="flex">
     
        <Button
          label="Insert Table"
          show-label
          icon-size="xl"
          icon="heroicons:table-cells-20-solid"
          command="insertTable"
          @click="insertTable"
        />
        <Button
          label="Fix Table"
          show-label
          :disabled="!editor.isActive('table')"
          icon-size="xl"
          icon="heroicons:table-cells"
          command="fixTable"
          @click="executeCommand"
        />
      </div>
    </div>
    <Divider />
    <div class="table-toolbar-col">
      <div class="flex">
        <Button
          label="Insert Row Above"
          icon-size="md"
           :disabled="!editor.isActive('table')"
          icon="ant-design:insert-row-above-outlined"
          command="addRowBefore"
          
          @click="executeCommand"
        />
        <Button
          label="Insert Row Below"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="ant-design:insert-row-below-outlined"
          command="addRowAfter"
          @click="executeCommand"
        />
        <Button
          label="Delete Row"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="hugeicons:row-delete"
          command="deleteRow"
          @click="executeCommand"
        />
      </div>

      <div class="flex">
        <Button
          label="Insert Column Before"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="ant-design:insert-row-left-outlined"
          command="addColumnBefore"
          @click="executeCommand"
        />
        <Button
          label="Insert Column After"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="ant-design:insert-row-right-outlined"
          command="addColumnAfter"
          @click="executeCommand"
        />
        <Button
          label="Delete Column"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="hugeicons:column-delete"
          command="deleteColumn"
          @click="executeCommand"
        />
      </div>
    </div>
    <Divider />
    <div class="table-toolbar-col">
      <div class="flex">
        <Button
          label="Merge Cells"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="icon-park-outline:merge-cells"
          command="mergeCells"
          @click="executeCommand"
        />
      </div>
      <div class="flex">
        <Button
          label="Split Cells"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="icon-park-outline:split-cells"
          command="splitCell"
          @click="executeCommand"
        />
      </div>
    </div>
    <Divider />
    <div class="table-toolbar-col">
      <div class="flex">
        <Button
          label="Header Row"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="jam:table-top-header"
          command="toggleHeaderRow"
          @click="executeCommand"
        />
        <Button
          label="Header Column"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="jam:table-left-header"
          command="toggleHeaderColumn"
          @click="executeCommand"
        />
      </div>
      <div class="flex">
        <Button
          label="Header Cell"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="tabler:rectangle-filled"
          command="toggleHeaderCell"
          @click="executeCommand"
        />
      </div>
    </div>
    <Divider />
    <div class="table-toolbar-col">
      <div class="flex">
        <Button
          label="Next Cell"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="mdi:chevron-right-box-outline"
          command="goToNextCell"
          @click="executeCommand"
        />
      </div>
      <div class="flex">
        <Button
          label="Previous Cell"
           :disabled="!editor.isActive('table')"
          icon-size="md"
          icon="mdi:chevron-left-box-outline"
          command="goToPreviousCell"
          @click="executeCommand"
        />
      </div>
    </div>
    <Divider />
    <div class="table-toolbar-col">
      <div class="flex">
        <Button
          label="Delete Table"
           :disabled="!editor.isActive('table')"
          show-label
          icon-size="xl"
          icon="mdi:table-large-remove"
          command="deleteTable"
          @click="executeCommand"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted } from "vue";
import Button from "./Button.vue"; // Import Button component
import Divider from "./Divider.vue"; // Import Divider component
import { useDocEditor } from "~/app/shared/composables/core/editors/useDocEditor";
import { useGlobalModal } from "~/composables/useGlobalModal.ts"; // Import globalModal composable
import InsertTableModal from "../modals/InsertTableModal.vue";

const { openModal } = useGlobalModal(); // Get the openModal function from globalModal composable
const { executeCommand, editor } = useDocEditor();

const insertTable = () => {
  const modal = openModal({
    title: "Insert Link",
    component: InsertTableModal,
    icon: "i-heroicons:link",
    okText: "OK",
    cancelText: "Cancel",
    onCancel() {
      modal.close();
    },
    onOk({ component }) {
      component.emitter.once("insert", (options) => {
        console.log(options);

        executeCommand("insertTable", options);

        modal.close();
      });
      component.insert();
    },
  });
};

const emit = defineEmits(["ready"]);
onMounted(() => {
  emit("ready");
});
defineExpose({
  insertTable,
});
</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.table-toolbar-tab {
  @apply flex align-middle justify-center;
}
.table-toolbar-col {
  @apply flex flex-col space-y-2 align-middle justify-center;
}
</style>

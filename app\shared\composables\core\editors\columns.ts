import { Node } from '@tiptap/core';

export const Columns = Node.create({
  name: 'columns',
  group: 'block',
  content: 'column+', // Allows one or more 'column' nodes inside

  addAttributes() {
    return {
      style: {
        default: '',
      },
      // Add any attributes you need for your columns container (e.g., class)
      class: {
        default: 'columns-container',
      },
    };
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', HTMLAttributes, 0]; // Renders a div with attributes and content
  },

  parseHTML() {
    return [{ tag: 'div[class="columns-container"]' }]; // How Tiptap recognizes this node from HTML
  },
});
 

export const Column = Node.create({
  name: 'column',
  group: 'block',
  content: 'block+', // Allows block-level content inside
  
  addAttributes() {
    return {
      // Add attributes for individual columns (e.g., class for width)
      class: {
        default: 'column',
      },
    };
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', HTMLAttributes, 0];
  },

  parseHTML() {
    return [{ tag: 'div[class="column"]' }];
  },
});

import { Extension } from '@tiptap/core';

export const ColumnsExtension = Extension.create({
  name: 'columnsExtension',
  addExtensions() {
    return [Columns, Column];
  },
  addCommands() {
    return {
      insertColumns:
        (count: number = 2) =>
        ({ commands }: { commands: any }) => {
          if (count < 1) return false;
          // Build columns node with the specified number of column children
          const columnsNode = {
            type: 'columns',
            attrs: { style: `--grid-cols-count: repeat(${String(count)} ,1fr)`  },
            content: Array.from({ length: count }, () => ({
              type: 'column',
              attrs: {},
              content: [
                {
                  type: 'paragraph',
                  attrs: {},
                  content: [],
                },
              ],
            })),
          };
        
          
          return commands.insertContent(columnsNode);
        },
    } as Partial<import('@tiptap/core').RawCommands>;
  },
});

export default ColumnsExtension
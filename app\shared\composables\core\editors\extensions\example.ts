// Example usage of Tiptap Pages Extension
import { Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { TiptapPages, createCustomFormatFromCm, cmToPixels, inchToPixels } from './index'

// Basic usage
export function createBasicPagesEditor(element: Element) {
  return new Editor({
    element,
    extensions: [
      StarterKit,
      TiptapPages.configure({
        format: 'A4',
        showPageNumbers: true,
        showHeaders: true,
        showFooters: true,
        documentTitle: 'My Document',
      }),
    ],
    content: '', // Will auto-create first page
  })
}

// Advanced usage with custom format
export function createAdvancedPagesEditor(element: Element) {
  // Create a custom page format
  const customFormat = createCustomFormatFromCm(
    'Custom Legal',
    21.59, // width in cm
    35.56, // height in cm
    {
      top: 3.0,
      right: 2.5,
      bottom: 3.0,
      left: 2.5,
    }
  )

  return new Editor({
    element,
    extensions: [
      StarterKit,
      TiptapPages.configure({
        format: customFormat,
        headerHeight: 60,
        footerHeight: 60,
        pageGap: 40,
        pageBreakBackground: '#f0f9ff',
        showPageNumbers: true,
        showHeaders: true,
        showFooters: true,
        documentTitle: 'Legal Document',
        customHeader: {
          height: 60,
          content: 'CONFIDENTIAL',
          alignment: 'right',
        },
        customFooter: {
          height: 60,
          showPageNumbers: true,
          alignment: 'center',
        },
      }),
    ],
    content: '',
  })
}

// Example commands usage
export function demonstrateCommands(editor: Editor) {
  // Set page format
  editor.commands.setPageFormat('Letter')
  
  // Set document title
  editor.commands.setDocumentTitle('My New Document')
  
  // Insert a new page
  editor.commands.insertPage()
  
  // Insert a manual page break
  editor.commands.insertPageBreak()
  
  // Toggle page numbers
  editor.commands.togglePageNumbers()
  
  // Set custom header content
  editor.commands.setHeaderContent('DRAFT - CONFIDENTIAL', 'right')
  
  // Set custom footer content
  editor.commands.setFooterContent('© 2024 Legal SaaS', 'left')
  
  // Update page layout
  editor.commands.updatePageLayout({
    headerHeight: 70,
    footerHeight: 70,
    pageGap: 60,
    pageBreakBackground: '#fef3c7',
  })
}

// Example with different page formats
export function createMultiFormatExample() {
  const formats = {
    // Standard formats
    a4: 'A4',
    a3: 'A3',
    a5: 'A5',
    letter: 'Letter',
    legal: 'Legal',
    tabloid: 'Tabloid',
    
    // Custom formats
    customCm: createCustomFormatFromCm('Custom CM', 20, 25, {
      top: 2,
      right: 1.5,
      bottom: 2,
      left: 1.5,
    }),
    
    customInch: createCustomFormatFromInch('Custom Inch', 8, 10, {
      top: 1,
      right: 0.75,
      bottom: 1,
      left: 0.75,
    }),
  }
  
  return formats
}

// Utility functions examples
export function utilityExamples() {
  // Convert measurements
  const a4WidthPx = cmToPixels(21.0)
  const letterHeightPx = inchToPixels(11)
  
  console.log('A4 width in pixels:', a4WidthPx)
  console.log('Letter height in pixels:', letterHeightPx)
  
  // Create custom formats
  const businessCard = createCustomFormatFromCm('Business Card', 8.5, 5.5, {
    top: 0.5,
    right: 0.5,
    bottom: 0.5,
    left: 0.5,
  })
  
  return {
    a4WidthPx,
    letterHeightPx,
    businessCard,
  }
}

// Event handling examples
export function setupEventHandlers(editor: Editor) {
  // Listen for page count changes
  editor.on('update', () => {
    const pageCount = editor.storage.tiptapPages?.pageCount || 0
    console.log('Current page count:', pageCount)
  })
  
  // Listen for format changes
  editor.on('transaction', ({ transaction }) => {
    if (transaction.getMeta('formatChanged')) {
      const newFormat = editor.storage.tiptapPages?.format
      console.log('Page format changed to:', newFormat)
    }
  })
  
  // Custom page overflow handler
  editor.on('update', () => {
    let hasOverflow = false
    editor.state.doc.descendants((node) => {
      if (node.type.name === 'pageContent' && node.attrs.overflow) {
        hasOverflow = true
        return false
      }
    })
    
    if (hasOverflow) {
      console.log('Page overflow detected!')
      // Handle overflow (e.g., show warning, auto-break page)
    }
  })
}

// CSS integration example
export function applyCSSIntegration(editorContainer: HTMLElement) {
  // Add the pages container class
  editorContainer.classList.add('tiptap-pages-container')
  
  // Apply custom CSS variables
  editorContainer.style.setProperty('--page-gap', '60px')
  editorContainer.style.setProperty('--page-break-background', '#f0f9ff')
  editorContainer.style.setProperty('--header-height', '70px')
  editorContainer.style.setProperty('--footer-height', '70px')
  
  // Add custom styles
  const style = document.createElement('style')
  style.textContent = `
    .tiptap-pages-container .tiptap-page {
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    .tiptap-pages-container .tiptap-page-header {
      background: linear-gradient(to right, #f9fafb, #f3f4f6);
    }
    
    .tiptap-pages-container .tiptap-page-footer {
      background: linear-gradient(to right, #f3f4f6, #f9fafb);
    }
  `
  document.head.appendChild(style)
}

// Complete integration example
export function createCompleteExample(element: Element) {
  const editor = new Editor({
    element,
    extensions: [
      StarterKit.configure({
        // Disable default document structure
        document: false,
      }),
      TiptapPages.configure({
        format: 'A4',
        headerHeight: 50,
        footerHeight: 50,
        pageGap: 50,
        pageBreakBackground: '#f6f3f4',
        showPageNumbers: true,
        showHeaders: true,
        showFooters: true,
        documentTitle: 'Legal Document Template',
      }),
    ],
    content: '',
    onCreate: ({ editor }) => {
      console.log('Pages editor created with', editor.storage.tiptapPages?.pageCount, 'pages')
    },
    onUpdate: ({ editor }) => {
      const storage = editor.storage.tiptapPages
      if (storage) {
        console.log('Document updated:', {
          pageCount: storage.pageCount,
          format: storage.format,
          documentTitle: storage.documentTitle,
        })
      }
    },
  })
  
  // Apply CSS integration
  if (element instanceof HTMLElement) {
    applyCSSIntegration(element)
  }
  
  // Setup event handlers
  setupEventHandlers(editor)
  
  return editor
}

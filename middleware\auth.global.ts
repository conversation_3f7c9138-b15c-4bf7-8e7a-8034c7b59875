// middleware/auth.ts
import { defineNuxtRouteMiddleware, navigateTo } from '#app';
import { useAuth } from '../composables/useAuth.js';
import type { RouteLocationNormalized } from 'vue-router';
 
export default defineNuxtRouteMiddleware(async (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
  // Allow Chrome DevTools to fetch its specific JSON file without authentication
  if (to.path.startsWith('/.well-known/appspecific/com.chrome.devtools.json')) {
    return;
  }

  // Whitelist common static asset paths
  const staticAssetPatterns = [
    /^\/icons\//,
    /^\/images\//,
    /^\/fonts\//,
    /^\/css\//,
    /^\/screenshots\//,
    /^\/products\.json$/,
    /^\/site\.webmanifest$/,
    /^\/sw\.js$/,
    /\.(png|jpe?g|gif|svg|webp|ico)$/i, // Common image extensions
    /\.(woff2?|ttf|otf|eot)$/i, // Common font extensions
    // CSS and JS are usually handled by the build, but good to have if linked directly
    // /\.(css|js)$/i
  ];

  if (staticAssetPatterns.some(pattern => pattern.test(to.path))) {
    return; // Allow access to static assets
  }

  const { isAuthenticated, isLoading } = useAuth(); // Removed initAuth from here


  // publicPaths definition and other logic remains the same initially
  const publicPaths = [
    '/', // Homepage
    '/about',
    '/contact',
    '/features',
    '/get-started',
    '/pricing',
    '/privacy-policy',
    '/terms-of-service',
    '/editor-test'
    // Add any other public paths here
  ];

  const isAuthPage = to.path.startsWith('/auth/');
  const isPublicPage = publicPaths.includes(to.path) ||  to.path.startsWith('/test');

  // If authentication is still loading, let navigation proceed.
  // The UI should ideally handle the loading state (e.g., show a spinner).
  // Or, specific pages can have their own checks.
  if (isLoading.value) {
    return; // Allow navigation while auth state is being determined
  }

  if (isAuthenticated.value) {
          
    // If authenticated and trying to access an auth page (e.g., login, register)
    if (isAuthPage) {

      if (to.path !== '/dashboard') {
        console.log('[Auth Middleware] Authenticated user accessing auth page. Redirecting to /dashboard.');
        return navigateTo('/dashboard');
      }
    }
      
    // Authenticated user can access any other page (dashboard, public, etc.)
  } else {
    // User is NOT authenticated
    // If trying to access a protected page (not an auth page and not a public page)
    if (!isAuthPage && !isPublicPage) {
      if (to.path !== '/auth/login') {
        console.log(`[Auth Middleware] Unauthenticated user accessing protected route ${to.path}. Redirecting to /auth/login.`);
        // Include redirect parameter to return to the original page after login
        const redirectUrl = `/auth/login?redirect=${encodeURIComponent(to.fullPath)}`;
        return navigateTo(redirectUrl);
      }
    }
    // Unauthenticated user can access auth pages and public pages
         
  }
});
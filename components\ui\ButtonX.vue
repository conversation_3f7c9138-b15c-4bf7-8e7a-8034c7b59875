<template>
  <component
    :is="componentTag"
    ref="buttonRef"
    :type="componentTag === 'button' ? props.type : undefined"
    :href="componentTag === 'a' ? props.href : undefined"
    :to="componentTag === 'NuxtLink' ? props.to : undefined"
    :disabled="props.disabled || props.loading"
    :aria-label="props.ariaLabel"
    :class="[
      'relative inline-flex items-center justify-center font-medium transition-all duration-200 ease-in-out',
      'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
      sizeClasses,
      variantClasses,
      shapeClasses,
      disabledClasses,
      props.fullWidth ? 'w-full' : '',
      props.class
    ]"
    @click="handleClick"
  >
    <!-- Loading State -->
    <template v-if="props.loading">
      <UiSpinner
        :size="spinnerSize"
        :class="[
          'text-current',
          ($slots.default && props.shape !== 'circle') ? 'mr-2' : ''
        ]"
      />
      <span v-if="props.shape !== 'circle' && ($slots.default || props.loadingText)">
        {{ props.loadingText || 'Loading...' }}
      </span>
    </template>

    <!-- Normal State -->
    <template v-else>
      <!-- Leading Icon -->
      <Icon
        v-if="props.leadingIcon"
        :name="props.leadingIcon"
        :class="[
          iconSizeClasses,
          $slots.default ? 'mr-2' : ''
        ]"
      />

      <!-- Button Content -->
      <span v-if="$slots.default" class="truncate">
        <slot />
      </span>

      <!-- Trailing Icon -->
      <Icon
        v-if="props.trailingIcon"
        :name="props.trailingIcon"
        :class="[
          iconSizeClasses,
          $slots.default ? 'ml-2' : ''
        ]"
      />
    </template>
  </component>
</template>

<script setup lang="ts">
interface Props {
  type?: 'button' | 'submit' | 'reset'
  variant?: 'contained' | 'outline' | 'ghost' | 'flat' | 'gradient'
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'gray'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  shape?: 'default' | 'pill' | 'circle' | 'square'
  disabled?: boolean
  loading?: boolean
  loadingText?: string
  to?: string
  href?: string
  leadingIcon?: string
  trailingIcon?: string
  ariaLabel?: string
  fullWidth?: boolean
  class?: string
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'button',
  variant: 'contained',
  color: 'primary',
  size: 'md',
  shape: 'default',
  disabled: false,
  loading: false,
  fullWidth: false,
})

const emit = defineEmits<Emits>()

const buttonRef = ref<HTMLElement | null>(null)

// Component tag logic
const componentTag = computed(() => {
  if (props.to) return 'NuxtLink'
  if (props.href) return 'a'
  return 'button'
})

// Size classes
const sizeClasses = computed(() => {
  const sizeMap = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  }
  return sizeMap[props.size]
})

// Icon size classes
const iconSizeClasses = computed(() => {
  const iconSizeMap = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6'
  }
  return iconSizeMap[props.size]
})

// Spinner size
const spinnerSize = computed(() => {
  const spinnerSizeMap = {
    xs: 'xs',
    sm: 'sm',
    md: 'sm',
    lg: 'md',
    xl: 'lg'
  }
  return spinnerSizeMap[props.size]
})

// Shape classes
const shapeClasses = computed(() => {
  const shapeMap = {
    default: 'rounded-md',
    pill: 'rounded-full',
    circle: 'rounded-full aspect-square',
    square: 'rounded-none'
  }
  return shapeMap[props.shape]
})

// Disabled classes
const disabledClasses = computed(() => {
  if (props.disabled || props.loading) {
    return 'opacity-50 cursor-not-allowed pointer-events-none'
  }
  return 'cursor-pointer hover:scale-[1.02] active:scale-[0.98]'
})

// Variant classes with color support
const variantClasses = computed(() => {
  const colorMap = {
    primary: {
      bg: 'bg-blue-600',
      bgHover: 'hover:bg-blue-700',
      bgLight: 'bg-blue-100',
      bgLightHover: 'hover:bg-blue-200',
      text: 'text-blue-600',
      textOnBg: 'text-white',
      border: 'border-blue-600',
      focusRing: 'focus-visible:ring-blue-500/50'
    },
    secondary: {
      bg: 'bg-gray-600',
      bgHover: 'hover:bg-gray-700',
      bgLight: 'bg-gray-100',
      bgLightHover: 'hover:bg-gray-200',
      text: 'text-gray-600',
      textOnBg: 'text-white',
      border: 'border-gray-600',
      focusRing: 'focus-visible:ring-gray-500/50'
    },
    success: {
      bg: 'bg-green-600',
      bgHover: 'hover:bg-green-700',
      bgLight: 'bg-green-100',
      bgLightHover: 'hover:bg-green-200',
      text: 'text-green-600',
      textOnBg: 'text-white',
      border: 'border-green-600',
      focusRing: 'focus-visible:ring-green-500/50'
    },
    warning: {
      bg: 'bg-yellow-600',
      bgHover: 'hover:bg-yellow-700',
      bgLight: 'bg-yellow-100',
      bgLightHover: 'hover:bg-yellow-200',
      text: 'text-yellow-600',
      textOnBg: 'text-white',
      border: 'border-yellow-600',
      focusRing: 'focus-visible:ring-yellow-500/50'
    },
    danger: {
      bg: 'bg-red-600',
      bgHover: 'hover:bg-red-700',
      bgLight: 'bg-red-100',
      bgLightHover: 'hover:bg-red-200',
      text: 'text-red-600',
      textOnBg: 'text-white',
      border: 'border-red-600',
      focusRing: 'focus-visible:ring-red-500/50'
    },
    info: {
      bg: 'bg-cyan-600',
      bgHover: 'hover:bg-cyan-700',
      bgLight: 'bg-cyan-100',
      bgLightHover: 'hover:bg-cyan-200',
      text: 'text-cyan-600',
      textOnBg: 'text-white',
      border: 'border-cyan-600',
      focusRing: 'focus-visible:ring-cyan-500/50'
    },
    gray: {
      bg: 'bg-gray-600',
      bgHover: 'hover:bg-gray-700',
      bgLight: 'bg-gray-100',
      bgLightHover: 'hover:bg-gray-200',
      text: 'text-gray-600',
      textOnBg: 'text-white',
      border: 'border-gray-600',
      focusRing: 'focus-visible:ring-gray-500/50'
    }
  }

  const colors = colorMap[props.color] || colorMap.primary
  const focusRing = colors.focusRing

  const variantMap = {
    contained: `${colors.bg} ${colors.textOnBg} ${colors.bgHover} hover:shadow-md border border-transparent ${focusRing}`,
    outline: `bg-transparent ${colors.text} border ${colors.border} ${colors.bgHover} hover:${colors.textOnBg} hover:shadow-md ${focusRing}`,
    ghost: `bg-transparent ${colors.text} hover:${colors.bgLight} dark:hover:bg-gray-800 border border-transparent ${focusRing}`,
    flat: `bg-transparent ${colors.text} hover:${colors.bgLight} dark:hover:bg-gray-800 border border-transparent ${focusRing}`,
    gradient: `bg-gradient-to-r from-${props.color}-600 to-${props.color}-700 hover:from-${props.color}-700 hover:to-${props.color}-800 ${colors.textOnBg} hover:shadow-lg border border-transparent ${focusRing}`
  }

  return variantMap[props.variant] || variantMap.contained
})

// Click handler
const handleClick = (event: MouseEvent) => {
  console.log(event);
  
  if (props.disabled || props.loading) return
  emit('click', event)
}
</script>

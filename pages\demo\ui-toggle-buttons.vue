<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">UI Toggle Buttons Demo</h1>
    
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-2">Basic Toggle</h2>
      <UiToggleButtons v-model="selectedOption" :options="toggleOptions" />
      <p class="mt-2">Selected: <span class="font-mono bg-gray-100 dark:bg-gray-800 p-1 rounded">{{ selectedOption }}</span></p>
    </div>

    <div>
      <h2 class="text-lg font-semibold mb-2">With a Disabled Option</h2>
      <UiToggleButtons v-model="selectedOption2" :options="toggleOptionsWithDisabled" />
      <p class="mt-2">Selected: <span class="font-mono bg-gray-100 dark:bg-gray-800 p-1 rounded">{{ selectedOption2 }}</span></p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const selectedOption = ref('option1');
const toggleOptions = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
  { label: 'Option 3', value: 'option3' },
];

const selectedOption2 = ref('first');
const toggleOptionsWithDisabled = [
  { label: 'First', value: 'first' },
  { label: 'Second (Disabled)', value: 'second', disabled: true },
  { label: 'Third', value: 'third' },
];
</script>
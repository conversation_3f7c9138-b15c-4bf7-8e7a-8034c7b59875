<template>
  <div class="tooltip" ref="containerRef">
    <slot name="trigger" v-bind="triggers"></slot>
    <Teleport to="body">
      <Transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div
          class="tooltip-content"
          v-if="showTooltip"
          :class="{ show: isOpen }"
          :style="style"
          ref="tooltipRef"
        >
          <slot name="content">{{ content }}</slot>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>
<script setup lang="ts">
import { useTemplateRef, ref, computed, onMounted, onBeforeUnmount, useSlots } from "vue";
import { onClickOutside } from "@vueuse/core";
const slots = useSlots();

type TooltipPlacement = "top" | "bottom" | "left" | "right";

type TooltipTrigger = "hover" | "click";
type TooltipVariant =
  | "default"
  | "dark"
  | "light"
  | "primary"
  | "success"
  | "warning"
  | "error";
type TooltipSize = "sm" | "md" | "lg";

interface Props {
  content?: string;
  placement?: TooltipPlacement;
  trigger?: TooltipTrigger;
  variant?: TooltipVariant;
  size?: TooltipSize;
  delay?: number;
  hideDelay?: number;
  disabled?: boolean;
  showArrow?: boolean;
  offset?: number;
  ariaDescribedby?: string;
  maxWidth?: string;
  interactive?: boolean;
  animation?: boolean;
  showTooltip?: boolean;
}

interface Emits {
  (e: "show"): void;
  (e: "hide"): void;
  (e: "toggle", visible: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  placement: "top",
  trigger: "hover",
  variant: "default",
  size: "md",
  delay: 200,
  hideDelay: 200,
  disabled: false,
  showArrow: true,
  offset: 8,
  maxWidth: "200px",
  interactive: false,
  animation: true,
  showTooltip: true,
});

const emit = defineEmits<Emits>();

const isOpen = ref(false);
const containerRef = useTemplateRef<HTMLElement>("containerRef");
const tooltipRef = useTemplateRef<HTMLElement>("tooltipRef");

const positionX = ref(0);
const positionY = ref(0);
const style = computed(() => ({
  top: positionY.value + "px",
  left: positionX.value + "px",
}));
const calculatePosition = () => {
  if (!containerRef.value || !tooltipRef.value) return;
  const rect = containerRef.value.getBoundingClientRect();
  const tooltipRect = tooltipRef.value.getBoundingClientRect();
  const padding = 5;
  switch (props.placement) {
    default:
    case "top":
      positionY.value = rect.y - rect.height;
      positionX.value = rect.x - (tooltipRect.width - rect.width) / 2;
      break;
    case "bottom":
      positionY.value = rect.y + rect.height;
      positionX.value = rect.x - (tooltipRect.width - rect.width) / 2;
      break;
    case "left":
      positionY.value = rect.y - (tooltipRect.height - rect.height) / 2;
      positionX.value = rect.x - tooltipRect.width - padding;
      break;
    case "right":
      positionY.value = rect.y - (tooltipRect.height - rect.height) / 2;
      positionX.value = rect.x + rect.width + padding;
      break;
  }
};

const show = () => {
  if (isOpen.value) return;
  calculatePosition();
  setTimeout(() => {
    isOpen.value = true;
    emit("show");
  }, props.delay);
};

const hide = () => {
  setTimeout(() => {
    isOpen.value = false;
    emit("hide");
  }, props.hideDelay);
};

const toggle = () => {
  isOpen.value ? hide() : show();
  emit("toggle", !isOpen.value);
};

const onclick = () => toggle();
const onmouseover = () => show();
const onmouseout = () => hide();
const triggers: Record<string, Function> = {};

if (props.trigger == "hover") {
  triggers.onmouseover = onmouseover;
  triggers.onmouseout = onmouseout;
} else {
  triggers.onclick = onclick;
  onClickOutside(containerRef, hide);
}

onMounted(() => {
  window.addEventListener("scroll", calculatePosition, true);
  window.addEventListener("resize", calculatePosition);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", calculatePosition, true);
  window.removeEventListener("resize", calculatePosition);
});
</script>
<style scoped>
@reference '~/assets/css/tailwind.css';
.tooltip-content {
  @apply absolute rounded-sm bg-white p-1 shadow-xs text-xs border border-gray-200  opacity-0 -z-10 ;
}

.tooltip-content.show {
  @apply flex opacity-100 z-50;
}
</style>

<template>
    <div :class="`columns-container grid-cols-${props.columns}`">
         <a :class="[`column-selector`, { hovered: isHovered(col) }]" v-for="col in props.columns" key="col" @mouseover="hover(col)" @click="select(col)"> {{col}} </a>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
interface Props {
    columns?: number
}
const props = withDefaults(defineProps<Props>(), {
    columns: 6
})
const emit = defineEmits(['select'])
const hovered = ref(0);
const hover = function (col) {
    hovered.value = col
}
const isHovered = function (col) {
    return hovered.value >= col
}
const select = function (col) {
 
    
    emit('select', col)
}
</script>
<style scoped>
@reference '~/assets/css/tailwind.css';
.columns-container {
   @apply grid space-x-2 w-[20rem]; 
}
.column-selector {
  @apply flex items-center justify-center h-[5rem] w-[3rem] bg-gray-200 border border-gray-300 rounded-sm cursor-pointer text-sm font-bold text-gray-400;
}
 
.column-selector.hovered {
     @apply border-brandPrimary-400 bg-brandPrimary-100;
} 
</style>
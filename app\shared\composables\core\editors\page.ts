import { Extension, Node, mergeAttributes } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import { AutoPageBreak } from './AutoPageBreak'
export const Page = Node.create({
    name: 'page',
    group: 'block',
    content: 'block+',
    isolating: true,
    draggable: false,


    addAttributes() {
        return {
            size: {
                default: 'A4',
            },
            margin: {
                default: '20mm',
            },
            id: {
                default: null,
            },
        }
    },

    parseHTML() {
        return [
            {
                tag: 'div[data-type="page"]',
            },
        ]
    },

    renderHTML({ HTMLAttributes }) {
        return ['div', mergeAttributes(HTMLAttributes, {
            'data-type': 'page',
            class: 'page',
        }), 0]
    },

    addNodeView() {
        return ({ node, HTMLAttributes }) => {
         
            const dom = document.createElement('div')
            dom.classList.add('page')
            dom.setAttribute('data-type', 'page')
            dom.setAttribute('data-id', '1')

            dom.style.width = '210mm'
            dom.style.height = '297mm'
            dom.style.padding = '20mm'
            dom.style.boxSizing = 'border-box'
            dom.style.backgroundColor = 'white'
            dom.style.border = '1px solid #ccc'
            dom.style.pageBreakAfter = 'always'

            // This is the editable part
            const contentDOM = document.createElement('div')
            contentDOM.classList.add('page-content')
            dom.appendChild(contentDOM)

            return {
                dom,
                contentDOM, // This tells ProseMirror where to render the inner content
            }
        }
    }
})

import { Plugin, PluginKey } from 'prosemirror-state'

const CP = () => {
    return new Plugin({
    key: new PluginKey('auto-page-break'),
    view(editorView) {
        return {
            update: (view, prevState) => {
                // Called whenever the view's state is updated
                console.log('Plugin view updated', view, prevState);
            },
            destroy: () => {
                // Called when the plugin view is destroyed
                console.log('Plugin view destroyed');
            }
        }
    }
    })
}

export const Doc = Extension.create({
    name: 'LegalEx',
    addExtensions() {
        return [ StarterKit, Page ]
    },
    addProseMirrorPlugins() {
        return [AutoPageBreak()]
    }
})
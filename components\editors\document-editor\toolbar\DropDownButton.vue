<template>
  <div class="relative" ref="containerRef">
    <!-- Main <PERSON> with Dropdown Arrow -->
    <ui-tooltip
      size="sm"
      variant="light"
      :content="props.label"
      :disabled="props.disabled"
      :show-tooltip="!props.showLabel"
    >
      <template #trigger="on">
        <div
          class="flex justify-center items-center hover:bg-gray-100 cursor-pointer pe-1"
          v-bind="on"
          @click.prevent.stop="handleMainClick"
        >
          <!-- Main Button -->
          <ui-button
            size="sm"
            variant="flat"
            color="gray"
            class="pe-0"
            :disabled="props.disabled"
            @click.prevent.stop="handleMainClick"
          >
            <div class="flex flex-col justify-center items-center">
              <slot v-if="$slots.default" />
              <Icon v-else :size="iconSize" :color="props.iconColor" :name="icon" />
              <span v-if="props.showLabel" class="mt-1 text-xs">{{ props.label }}</span>
            </div>
          </ui-button>
          <!-- <Button
            :disabled="props.disabled"
            :label="props.label"
            :icon-size="iconSize"
            :icon="icon"
            :icon-color="props.iconColor"
            :show-label="props.showLabel"
            :command="props.command"
            @click="handleMainClick"
            :class="[
              'rounded-r-none border-r-0 !pe-1',
              isOpen ? 'bg-gray-200 dark:bg-gray-700' : '',
            ]"
          /> -->

          <!-- Dropdown Arrow Button -->
          <ui-button
            size="xs"
            v-if="splited"
            variant="flat"
            color="gray"
            :disabled="props.disabled"
            :class="[
              'w-3 !p-0 m-0 ',
              'rounded-l-none border-l border-gray-300 dark:border-gray-600 px-1',
              isOpen ? 'bg-gray-200 dark:bg-gray-700' : '',
            ]"
            @click.prevent.stop="toggle"
          >
            <Icon
              size="calc(var(--spacing) * 2.5)"
              name="heroicons:chevron-down"
              :class="['transition-transform duration-200', isOpen ? 'rotate-180' : '']"
            />
          </ui-button>
          <Icon
            v-else
            size="calc(var(--spacing) * 2.5)"
            name="heroicons:chevron-down"
            :class="['transition-transform duration-200', isOpen ? 'rotate-180' : '']"
          />
        </div>
      </template>
    </ui-tooltip>

    <!-- Dropdown Menu -->
    <Teleport to="body">
      <Transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div
          v-if="isOpen"
          ref="menuRef"
          :class="[
            'menu-dropdown',
            'absolute z-20 mt-1 rounded-lg shadow-lg border p-2',
            'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700',
            widthClass,
            positionClass,
          ]"
          :style="style"
        >
          <slot name="dropdown" :close="close" :select="handleOptionSelect"></slot>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, useTemplateRef } from "vue";
import { onClickOutside } from "@vueuse/core";
import { isDescendant } from "~/utils/dom";

import Button from "./Button.vue";

interface Props {
  command?: string;
  disabled?: boolean;
  splited?: boolean;
  label?: string;
  icon?: string;
  iconSize?: string;
  iconColor?: string;
  showLabel?: boolean;
  width?: string;
  align?: "left" | "right" | "center";
  options?: Array<{
    label: string;
    value: string;
    icon?: string;
    command?: string;
    disabled?: boolean;
  }>;
}

interface Emits {
  (e: "click", command?: string): void;
  (e: "select", option: any): void;
  (e: "open", option: any): void;
  (e: "close", option: any): void;
  (e: "toggle", option: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  splited: true,
  iconSize: "md",
  iconColor: "currentColor",
  showLabel: false,
  width: "48",
  align: "left",
});

const emit = defineEmits<Emits>();

// const containerRef = ref<HTMLElement | null>(null);
const containerRef = useTemplateRef<HTMLElement>("containerRef");
const menuRef = useTemplateRef<HTMLElement>("menuRef");

const isOpen = ref(false);

// Computed properties
const icon = computed(() => props.icon || "heroicons:ellipsis-horizontal");

const iconSizeMap = {
  xs: "calc(var(--spacing) * 3)",
  sm: "calc(var(--spacing) * 4)",
  md: "calc(var(--spacing) * 5)",
  lg: "calc(var(--spacing) * 6)",
  xl: "calc(var(--spacing) * 7)",
  "2xl": "calc(var(--spacing) * 8)",
};

const iconSize = computed(() => {
  return iconSizeMap[props.iconSize];
});

const widthClass = computed(() => {
  const widthMap: Record<string, string> = {
    "32": "w-32",
    "40": "w-40",
    "48": "w-48",
    "56": "w-56",
    "64": "w-64",
    "72": "w-72",
    "80": "w-80",
    "96": "w-96",
    full: "w-full",
  };
  if (props.width.includes("px")) {
    return "";
  }
  return widthMap[props.width] || "w-48";
});

const positionX = ref(0);
const positionY = ref(0);
const style = computed(() => ({
  top: positionY.value + "px",
  left: positionX.value + "px",
}));
const calculatePosition = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect();
    positionX.value = rect.x;
    positionY.value = rect.y + rect.height;
  }
};

const positionClass = computed(() => {
  const alignMap: Record<string, string> = {
    left: "left-0",
    right: "right-0",
    center: "left-1/2 transform -translate-x-1/2",
  };
  return alignMap[props.align] || "left-0";
});

// Methods

const open = () => {
  if (isOpen.value) return;
  isOpen.value = true;
  calculatePosition();
  emit("open", true);
};

const close = () => {
  isOpen.value = false;
  emit("close", false);
};

const toggle = () => {
  isOpen.value ? close() : open();
  emit("toggle", !isOpen.value);
};

onClickOutside(containerRef, (ev) => {
  const menu = document.querySelector(".menu-dropdown");
  if (isDescendant(menu, ev.target)) return;
  close();
});

const handleMainClick = () => {
  if (props.disabled) return;
  props.splited ? emit("click", props.command) : toggle();
};

const handleOptionSelect = (option: any) => {
  emit("select", option);
  close();
};

onMounted(() => {
  window.addEventListener("scroll", calculatePosition, true);
  window.addEventListener("resize", calculatePosition);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", calculatePosition, true);
  window.removeEventListener("resize", calculatePosition);
});
</script>

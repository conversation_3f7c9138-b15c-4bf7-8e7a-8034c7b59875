<template>
  <div class="space-y-4">
    <div v-if="label" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </div>

    <!-- Upload Area -->
    <div
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      :class="[
        'relative border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200 cursor-pointer',
        isDragOver ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500',
        disabled ? 'opacity-50 cursor-not-allowed' : '',
        error ? 'border-red-300 bg-red-50 dark:bg-red-900/20' : ''
      ]"
      @click="!disabled && triggerFileInput()"
    >
      <!-- Preview Image -->
      <div v-if="previewUrl" class="mb-4">
        <img
          :src="previewUrl"
          :alt="selectedFile?.name || 'Image preview'"
          class="mx-auto h-20 w-20 rounded-lg object-cover border border-gray-200 dark:border-gray-700"
        />
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          {{ selectedFile?.name }}
        </p>
        <p class="text-xs text-gray-500 dark:text-gray-500">
          {{ formatFileSize(selectedFile?.size || 0) }}
        </p>
      </div>

      <!-- Upload Prompt -->
      <div v-else>
        <Icon name="heroicons:photo" class="mx-auto h-12 w-12 text-gray-400" />
        <div class="mt-4">
          <p class="text-lg font-medium text-gray-900 dark:text-gray-100">
            {{ uploadText }}
          </p>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {{ supportText }}
          </p>
        </div>
      </div>

      <!-- Hidden File Input -->
      <input
        ref="fileInput"
        type="file"
        :accept="acceptedTypes.join(',')"
        @change="handleFileSelect"
        class="hidden"
        :disabled="disabled"
      />

      <!-- Remove Button -->
      <button
        v-if="selectedFile && !disabled"
        @click.stop="removeFile"
        class="absolute top-2 right-2 p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200 transition-colors duration-200"
        type="button"
        :aria-label="removeAriaLabel"
      >
        <Icon name="heroicons:x-mark" class="h-4 w-4" />
      </button>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="text-sm text-red-600 dark:text-red-400">
      {{ error }}
    </div>

    <!-- Help Text -->
    <div v-if="helpText && !error" class="text-sm text-gray-500 dark:text-gray-400">
      {{ helpText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  modelValue?: File | null
  label?: string
  required?: boolean
  disabled?: boolean
  error?: string
  helpText?: string
  maxSize?: number // in bytes
  acceptedTypes?: string[]
  uploadText?: string
  supportText?: string
  removeAriaLabel?: string
}

interface Emits {
  (e: 'update:modelValue', value: File | null): void
  (e: 'change', value: File | null): void
  (e: 'error', error: string): void
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 2 * 1024 * 1024, // 2MB default
  acceptedTypes: () => ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  uploadText: 'Drop Image here or click to browse',
  supportText: 'JPEG, PNG, GIF, WebP up to 2MB',
  removeAriaLabel: 'Remove Image'
})

const emit = defineEmits<Emits>()

// State
const isDragOver = ref(false)
const selectedFile = ref<File | null>(props.modelValue || null)
const previewUrl = ref<string | null>(null)
const fileInput = ref<HTMLInputElement>()

// Computed
const acceptedMimeTypes = computed(() => {
  const mimeMap: Record<string, string> = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
  }
  return props.acceptedTypes.map(ext => mimeMap[ext]).filter(Boolean)
})

// Methods (defined before watch to avoid hoisting issues)
const updatePreview = () => {
  if (selectedFile.value) {
    const reader = new FileReader()
    reader.onload = (e) => {
      previewUrl.value = e.target?.result as string
    }
    reader.readAsDataURL(selectedFile.value)
  } else {
    previewUrl.value = null
  }
}

const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  selectedFile.value = newValue || null
  updatePreview()
}, { immediate: true })

// Other methods
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  if (!props.disabled) {
    isDragOver.value = true
  }
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  if (!props.disabled) {
    isDragOver.value = true
  }
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
  
  if (props.disabled) return
  
  const files = Array.from(e.dataTransfer?.files || [])
  if (files.length > 0) {
    processFile(files[0])
  }
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])
  if (files.length > 0) {
    processFile(files[0])
  }
}

const processFile = (file: File) => {
  // Validate file type
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
  if (!props.acceptedTypes.includes(fileExtension)) {
    const error = `File type not supported. Accepted types: ${props.acceptedTypes.join(', ')}`
    emit('error', error)
    return
  }

  // Validate file size
  if (file.size > props.maxSize) {
    const error = `File too large. Maximum size is ${formatFileSize(props.maxSize)}`
    emit('error', error)
    return
  }

  // Validate MIME type
  if (!acceptedMimeTypes.value.includes(file.type)) {
    const error = `Invalid file type: ${file.type}`
    emit('error', error)
    return
  }

  selectedFile.value = file
  updatePreview()
  emit('update:modelValue', file)
  emit('change', file)
}

const removeFile = () => {
  selectedFile.value = null
  previewUrl.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  emit('update:modelValue', null)
  emit('change', null)
}


</script>

import BulletList from '@tiptap/extension-bullet-list'
import OrderedList from '@tiptap/extension-ordered-list'

export const CustomBulletList = BulletList.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (attributes.class) {
            return { class: attributes.class }
          }
          return {}
        },
      },
    }
  },
})

export const CustomOrderedList = OrderedList.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (attributes.class) {
            return { class: attributes.class }
          }
          return {}
        },
      },
    }
  },
})

export default {CustomBulletList, CustomOrderedList}
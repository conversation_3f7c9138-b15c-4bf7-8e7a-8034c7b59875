/**
 * Enhanced Modal Composable
 * 
 * Comprehensive modal management with stacking, animations,
 * accessibility, and performance optimizations
 */

import { ref, computed, watch, nextTick, onUnmounted, readonly } from 'vue'
import { ComponentSize } from '../../types'
import { useLogger } from '../core/useLogger.js' // Added .js
import { useFocusTrap } from './useFocusTrap.js' // Added .js
import { useScrollLock } from './useScrollLock.js' // Added .js

// scrollLock can be initialized here as it's not target-dependent
// const scrollLock = useScrollLock(); // Already initialized globally or per instance later

// ============================================================================
// MODAL TYPES
// ============================================================================

export interface ModalConfig {
  id?: string
  title?: string
  icon?: string
  size?: ComponentSize | 'full'
  component?: any
  content?: string
  closable?: boolean
  maskClosable?: boolean
  keyboard?: boolean
  centered?: boolean
  destroyOnClose?: boolean
  zIndex?: number
  animation?: 'fade' | 'slide' | 'zoom' | 'none'
  persistent?: boolean
  maxWidth?: string
  maxHeight?: string
  className?: string
  debug?: boolean
  events?: Record<string, (event: any) => void>
  props?: Record<string, any>
  cancelText?: string
  okText?: string
  onOpen?: () => void
  onClose?: () => void
  onCancel?: () => void
  onOk?: (...args) => void
}

export interface ModalInstance {
  id: string
  config: ModalConfig
  isOpen: boolean
  isClosing: boolean
  zIndex: number
  element?: HTMLElement
}

// ============================================================================
// GLOBAL MODAL STATE
// ============================================================================

const modalStack = ref<ModalInstance[]>([])
const baseZIndex = ref(1000)
const nextModalId = ref(1)

// ============================================================================
// MODAL COMPOSABLE
// ============================================================================

export function useModal(config: ModalConfig = {}) {
  // ============================================================================
  // DEPENDENCIES
  // ============================================================================
  
  const logger = useLogger('Modal')
  // focusTrap will be initialized when the modal element is available
  let focusTrap: ReturnType<typeof useFocusTrap> | null = null;
  const modalElementRef = ref<HTMLElement | null>(null);
  
  // ============================================================================
  // STATE
  // ============================================================================
  
  const modalId = config.id || `modal_${nextModalId.value++}`
  const isOpen = ref(false)
  const isClosing = ref(false)
  const isAnimating = ref(false)
 

  

  
  // ============================================================================
  // COMPUTED
  // ============================================================================
  
  const modalInstance = computed(() => 
    modalStack.value.find(modal => modal.id === modalId)
  )
  
  const isTopModal = computed(() => {
    const topModal = modalStack.value[modalStack.value.length - 1]
    return topModal?.id === modalId
  })
  
  const zIndex = computed(() => modalInstance.value?.zIndex || baseZIndex.value)
  
  const modalClasses = computed(() => {
    const classes = ['modal']
    
    if (config.size) {
      classes.push(`modal--${config.size}`)
    }
    
    if (config.centered) {
      classes.push('modal--centered')
    }
    
    if (config.animation) {
      classes.push(`modal--${config.animation}`)
    }
    
    if (config.className) {
      classes.push(config.className)
    }
    
    if (isAnimating.value) {
      classes.push('modal--animating')
    }
    
    return classes
  })
  
  const modalStyles = computed(() => {
    const styles: Record<string, string> = {
      zIndex: zIndex.value.toString()
    }
    
    if (config.maxWidth) {
      styles.maxWidth = config.maxWidth
    }
    
    if (config.maxHeight) {
      styles.maxHeight = config.maxHeight
    }
    
    return styles
  })
  
  // ============================================================================
  // MODAL OPERATIONS
  // ============================================================================
  
  const open = async () => {
    if (isOpen.value || isClosing.value) return
    if (config.debug) {
      logger.debug('Opening modal', { modalId, config })
    }
    
    try {
      isAnimating.value = true
      
      // Create modal instance
      const instance: ModalInstance = {
        id: modalId,
        config,
        isOpen: true,
        isClosing: false,
        zIndex: baseZIndex.value + modalStack.value.length
        // element is optional, so it's fine if not present initially
      }
      
      // Add to stack
      modalStack.value.push(instance)
      isOpen.value = true
      
      // Wait for DOM update
      await nextTick()
      
      // Set up accessibility
      setupAccessibility()
      
      // Lock scroll if this is the first modal
      
      // Call onOpen callback
      if (config.onOpen) {
        config.onOpen()
      }
      if (config.debug) {
        logger.debug('Modal opened', { modalId })
      }
 
      
    } catch (error) {
      logger.error('Failed to open modal', { modalId, error })
    } finally {
      isAnimating.value = false
    }
  }
  
  const close = async (reason: 'close' | 'cancel' | 'ok' = 'close') => {
    if (!isOpen.value || isClosing.value) return
    
    if (config.debug) {
      logger.debug('Closing modal', { modalId, reason })
    }
    
    try {
      isClosing.value = true
      isAnimating.value = true
      
      // Call appropriate callback
      if (reason === 'cancel' && config.onCancel) {
        config.onCancel()
      } else if (reason === 'ok' && config.onOk) {
        config.onOk()
      } else if (config.onClose) {
        config.onClose()
      }
      
      // Remove from stack
      const index = modalStack.value.findIndex(modal => modal.id === modalId)
      if (index !== -1) {
        modalStack.value.splice(index, 1)
      }
      
      // Clean up accessibility
      cleanupAccessibility()
      
      // Unlock scroll if this was the last modal
      
      // Wait for animation
      if (config.animation && config.animation !== 'none') {
        await new Promise(resolve => setTimeout(resolve, 300))
      }
      
      isOpen.value = false
      isClosing.value = false
      
      if (config.debug) {
        logger.debug('Modal closed', { modalId, reason })
      }
      
    } catch (error) {
      logger.error('Failed to close modal', { modalId, error })
      isClosing.value = false
    } finally {
      isAnimating.value = false
    }
  }
  
  const toggle = () => {
    if (isOpen.value) {
      close()
    } else {
      open()
    }
  }
  

  // Default to close if not provided
  if (typeof config.onClose !== 'function') {
     config.onClose = () => { close() } // Default to close if not provided
  }
  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================
  
  const handleMaskClick = (event: MouseEvent) => {
    if (!config.maskClosable || !isTopModal.value) return
    
    const target = event.target as HTMLElement
    if (target.classList.contains('modal-mask')) {
      close('cancel')
    }
  }
  
  const handleKeydown = (event: KeyboardEvent) => {
    if (!isTopModal.value) return
    
    if (event.key === 'Escape' && config.keyboard !== false) {
      event.preventDefault()
      close('cancel')
    }
  }
  
  // ============================================================================
  // ACCESSIBILITY
  // ============================================================================
  
  const setupAccessibility = () => {
    // Set up focus trap
    const currentModalElement = document.querySelector(`[data-modal-id="${modalId}"]`) as HTMLElement
    if (currentModalElement) {
      modalElementRef.value = currentModalElement; // Assign to ref
      if (!focusTrap) { // Initialize focusTrap if not already
        focusTrap = useFocusTrap(modalElementRef, {
          // escapeDeactivates: false, // Example: if modal handles escape itself
          // clickOutsideDeactivates: false // Example: if modal handles this
        });
      }
      focusTrap.activate(); // No argument
      
      // Set ARIA attributes
      currentModalElement.setAttribute('role', 'dialog')
      currentModalElement.setAttribute('aria-modal', 'true')
      
      if (config.title) {
        const titleElement = currentModalElement.querySelector('.modal-title')
        if (titleElement) {
          const titleId = `modal-title-${modalId}`
          titleElement.id = titleId
          currentModalElement.setAttribute('aria-labelledby', titleId)
        }
      }
      
      // Store reference
      const instance = modalStack.value.find(modal => modal.id === modalId);
      if (instance) {
        instance.element = currentModalElement;
      }
    }
    
    // Add global event listeners
    document.addEventListener('keydown', handleKeydown)
  }
  
  const cleanupAccessibility = () => {
    // Deactivate focus trap
    // Deactivate focus trap for the current modal
    if (focusTrap && modalElementRef.value) {
      focusTrap.deactivate(); // No argument
    }
    focusTrap = null; // Reset focus trap instance for this modal
    modalElementRef.value = null;
    
    // Remove global event listeners
    document.removeEventListener('keydown', handleKeydown)
    
    // If there's another modal in the stack, activate its focus trap
    const previousModalInstance = modalStack.value[modalStack.value.length - 1];
    // If there's another modal in the stack, activate its focus trap
    // const previousModalInstance = modalStack.value[modalStack.value.length - 1]; // This was the redeclaration
    if (previousModalInstance?.element) { // previousModalInstance is already defined from the outer scope of cleanupAccessibility
        const prevModalElementRef = ref(previousModalInstance.element);
        const prevFocusTrap = useFocusTrap(prevModalElementRef, {});
        prevFocusTrap.activate();
    } else {
        // If no other modals, deactivate() should handle focus return.
    }
  }
  
  // ============================================================================
  // WATCHERS
  // ============================================================================
  
  watch(isOpen, (newValue) => {
    if (newValue) {
      // Modal opened
      document.body.classList.add('modal-open')
    } else {
      // Modal closed
      if (modalStack.value.length === 0) {
        document.body.classList.remove('modal-open')
      }
    }
  })
  
  // ============================================================================
  // CLEANUP
  // ============================================================================
  
  
  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================
  
  return {
    // State
    modalId,
    isOpen: readonly(isOpen),
    isClosing: readonly(isClosing),
    isAnimating: readonly(isAnimating),
    isTopModal,
    zIndex,
    
    // Computed
    modalClasses,
    modalStyles,
    
    // Methods
    open,
    close,
    toggle,
    
    // Event handlers
    handleMaskClick,
    handleKeydown
  }
}

// ============================================================================
// MODAL MANAGER
// ============================================================================

export function useModalManager() {
  const logger = useLogger('ModalManager')
  
  const openModals = computed(() => [...modalStack.value])
  
  const hasOpenModals = computed(() => modalStack.value.length > 0)
  
  const topModal = computed(() => 
    modalStack.value[modalStack.value.length - 1] || null
  )
  
  const closeAll = async () => {
    logger.info('Closing all modals')
    
    // Close modals in reverse order (top to bottom)
    const modalsToClose = [...modalStack.value].reverse()
    
    for (const modal of modalsToClose) {
      if (modal.config.onClose) {
        modal.config.onClose()
      }
    }
    
    modalStack.value = []
    document.body.classList.remove('modal-open')
  }
  
  const closeTop = async () => {
    const top = topModal.value
    if (top && top.config.onClose) {
      top.config.onClose()
    }
  }
  
  const getModal = (id: string) => {
    return modalStack.value.find(modal => modal.id === id)
  }
  
  const updateZIndex = (baseZ: number) => {
    baseZIndex.value = baseZ
    
    // Update all modal z-indexes
    modalStack.value.forEach((modal, index) => {
      modal.zIndex = baseZ + index
    })
  }
  
  return {
    openModals,
    hasOpenModals,
    topModal,
    closeAll,
    closeTop,
    getModal,
    updateZIndex
  }
}

// ============================================================================
// MODAL UTILITIES
// ============================================================================

export function createModal(config: ModalConfig) {
  return useModal(config)
}

export function openModal(config: ModalConfig) {
  const modal = useModal(config)
  modal.open()
  return modal
}

export function confirmModal(
  title: string,
  _content: string, // Prefixed unused
  _options: { // Prefixed unused
    okText?: string
    cancelText?: string
    type?: 'info' | 'warning' | 'error' | 'success'
  } = {}
): Promise<boolean> {
  return new Promise((resolve) => {
    const modal = useModal({
      title,
      size: ComponentSize.SM,
      closable: true,
      maskClosable: false,
      onOk: () => {
        modal.close('ok')
        resolve(true)
      },
      onCancel: () => {
        modal.close('cancel')
        resolve(false)
      },
      onClose: () => {
        resolve(false)
      }
    })
    
    modal.open()
  })
}

export function alertModal(
  title: string,
  _content: string, // Prefixed unused
  _type: 'info' | 'warning' | 'error' | 'success' = 'info' // Prefixed unused
): Promise<void> {
  return new Promise((resolve) => {
    const modal = useModal({
      title,
      size: ComponentSize.SM,
      closable: true,
      maskClosable: true,
      onClose: () => {
        resolve()
      }
    })
    
    modal.open()
  })
}

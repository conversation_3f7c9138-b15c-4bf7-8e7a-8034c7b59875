<template>
  <div class="flex flex-col space-y-5">
    <div
      class="flex justify-center items-center bg-gray-200 py-3"
      @mouseout="unHighlight"
    >
      <div
        :class="`grid w-[${props.cellSize + props.spacing}0px] h-[${
          props.cellSize + props.spacing
        }0px] space-x-[${props.spacing}px] space-y-[${props.spacing}px]`"
        :style="style"
      >
        <div
          :class="[
            `flex border border-gray-100 bg-white w-[${props.cellSize}px]  h-[${props.cellSize}px] text-xs`,
            { highlighted: isHighlighted(cell), active: isActive(cell) },
          ]"
          v-for="cell in cells"
          :key="cell"
          @mouseover="highlight(cell)"
          @click="activate(cell)"
        ></div>
      </div>
    </div>

    <div class="grid grid-cols-2 space-x-4">
      <ui-input
        id="columns"
        type="number"
        v-model="activeColumns"
        label="columns"
        min="1"
        max="20"
      />
      <ui-input
        id="rows"
        type="number"
        v-model="activeRows"
        label="rows"
        min="1"
        max="20"
      />
    </div>
    <div class="grid grid-cols-2 space-x-4">
      <ui-input id="cellSpacing" type="number" label="Cell Spacing" />
      <ui-input id="cellPadding" type="number" label="Cell Padding" />
    </div>
    <div class="grid grid-cols-2 space-x-4">
      <ui-toggle id="includeHeader" v-model="includeHeader" label="Include Header" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from "vue";
import mitt, { Emitter, Handler } from "mitt";
// Extend the mitt emitter to include a 'once' method
type Events = Record<string, unknown>;
interface MittWithOnce extends Emitter<Events> {
  once: (type: string, handler: Handler) => void;
}

interface Props {
  cellSize?: number;
  columns?: number;
  rows?: number;
  rowGap?: number;
  columnGap?: number;
  spacing?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cellSize: 30,
  columns: 10,
  rows: 8,
  spacing: 2,
});

const style = computed(() => {
  const s: Record<string, string> = {
    "grid-template-rows": `repeat(${props.rows}, ${props.cellSize}px)`,
    "grid-template-columns": `repeat(${props.columns},  ${props.cellSize}px)`,
  };

  if (props.spacing !== undefined) s.gap = `${props.spacing}px ${props.spacing}px`;

  return s;
});

const cells = computed(() => props.rows * props.columns);
const emitter = mitt() as MittWithOnce;

emitter.once = function (type: string, handler: Handler) {
  const onceHandler: Handler = (event) => {
    handler(event);
    emitter.off(type, onceHandler);
  };
  emitter.on(type, onceHandler);
};
type Cordinate = [number, number];

const highlighted = ref<Cordinate>([0, 0]); // [col, row]
const active = ref<Cordinate>([0, 0]); // [col, row]
const activeRows = computed({
  set(value: unknown) {
    active.value[1] = typeof value === "number" ? value : Number(value);
  },
  get() {
    return active.value[1];
  },
});
const activeColumns = computed({
  set(value: unknown) {
    active.value[0] = typeof value === "number" ? value : Number(value);
  },
  get() {
    return active.value[0];
  },
});
const unHighlight = () => (highlighted.value = [0, 0]);

const highlight = (cell) => {
  const [col, row] = callCordinates(cell);
  console.log([col, row]);
  
  highlighted.value = [col, row]
};
const activate = (cellNum) => {
  const [col, row] = callCordinates(cellNum);
  active.value = [col, row]
};

const includeHeader = ref(false);

const callCordinates = (cell) => {
  const col = ((cell - 1) % props.columns) + 1;
  const row = Math.floor((cell - 1) / props.columns) + 1;
  return [col, row];
};

const isActive = (cell) => {
  const [col, row] = callCordinates(cell);
  const [activeCol, activeRow] = active.value
  return  col <= activeCol && row <= activeRow
};

const isHighlighted = (cell) => {
  const [col, row] = callCordinates(cell);
  const [highlightedCol, highlightedRow] = highlighted.value
  return  col <= highlightedCol && row <= highlightedRow
};
  
const insert = function () {
    const [activeCol, activeRow] = active.value
  emitter.emit("insert", { rows: activeRow, cols: activeCol, withHeaderRow: includeHeader.value });
};
defineExpose({
  insert,
  emitter,
});
</script>
<style scoped>
@reference "~/assets/css/tailwind.css";
.highlighted,
.active {
  @apply border-brandPrimary-500 bg-brandPrimary-100 text-brandPrimary-500;
}
</style>

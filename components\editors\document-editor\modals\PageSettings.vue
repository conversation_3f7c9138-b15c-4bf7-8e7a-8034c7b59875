<template>
  <div class="flex flex-col w-[450px]">
    <div class="flex mb-4">
      <div class="w-1/4">
        <label> Orientation </label>
      </div>
      <div class="w-3/4">
        <UiToggleButtons v-model="orientation" :options="orientationOptions" />
      </div>
    </div>
    <div class="flex mb-4">
      <div class="w-1/4">
        <label> Size </label>
      </div>
      <div class="w-3/4">
        <label> <UiSelect id="size" size="sm" :options="sizes" v-model="size" @change="handleChange"/> </label>
      </div>
    </div>
    <div class="flex mb-4">
      <div class="w-1/4">
        <label> Dimensions </label>
      </div>
      <div class="w-3/4">
        <div class="flex space-x-4">
          <UiInput id="width" size="sm" type="number" v-model="width" label="width" trailing-text="cm"></UiInput>
          <UiInput id="height" size="sm" type="number" v-model="height" label="height" trailing-text="cm"></UiInput>
        </div>
      </div>
    </div>
    <div class="flex mb-4">
      <div class="w-1/4">
        <label> Margins </label>
        {{  }}
      </div>
      <div class="w-3/4">
        <div class="grid grid-cols-4 gap-4">
            <div :class="['margin-view default', {active: istMarginActive('default') }]" @click="setMargin('default')">
            default
            </div>
            <div :class="['margin-view narrow', {active: istMarginActive('narrow') }]" @click="setMargin('narrow')">
            Narrow
            </div>
            <div :class="['margin-view moderate', {active: istMarginActive('moderate') }]" @click="setMargin('moderate')">
            Moderate
            </div>
            <div :class="['margin-view wide', {active: istMarginActive('wide') }]" @click="setMargin('wide')">
           Wide
            </div>
        </div>
        <br />
        <div class="grid grid-cols-2 gap-4   ">
             <UiInput id="m-top" size="sm" type="number" :value="currentMarginDem?.top_mm" label="Top" trailing-text="cm" ></UiInput>
             <UiInput id="m-bottom" size="sm" type="number" :value="currentMarginDem?.bottom_mm" label="Bottom" trailing-text="cm"></UiInput>
             <UiInput id="m-left" size="sm" type="number" :value="currentMarginDem?.left_mm" label="Left" trailing-text="cm"></UiInput>
             <UiInput id="m-right" size="sm" type="number" :value="currentMarginDem?.right_mm" label="Right" trailing-text="cm"></UiInput>
        </div>
     
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";

const orientation = ref("portrait");
const orientationOptions = [
  { label: "Portrait", value: "portrait" },
  { label: "Landscape", value: "landscape" },
];

const size = ref("a4");
const sizes = ref([
  {
    label: "A4",
    value: "a4",
    description: "21cm x 29.7cm",
    width_in: 8.27,
    height_in: 11.69,
    width_mm: 210,
    height_mm: 297,
  },
  {
    label: "A3",
    value: "a3",
    description: "29.7cm x 42cm",
    width_in: 11.69,
    height_in: 16.54,
    width_mm: 297,
    height_mm: 420,
  },
  {
    label: "A5",
    value: "a5",
    description: "14.8cm x 21cm",
    width_in: 5.83,
    height_in: 8.27,
    width_mm: 148,
    height_mm: 210,
  },
]);

const width = ref(29.7);
const height = ref(42);

const handleChange = (val) => {
    console.log(val);
    
  const pageSize = sizes.find((item) => (item.value = val));
  if (pageSize) {
    width.value = parseFloat(String(pageSize.width_mm / 10));
    height.value = parseFloat(String(pageSize.height_mm / 10));
  }
}

const margin = ref('default');
const setMargin = m => margin.value = m
const istMarginActive = m => margin.value == m

const marginOptions = ref([
    {
        value: "default",
        top_mm: 25.4,
        left_mm: 31.8,
        bottom_mm: 25.4,
        right_mm: 31.8,
    },
     {
        value: "narrow",
        top_mm: 12.7,
        left_mm: 12.7,
        bottom_mm: 12.7,
        right_mm: 12.7,
    },
     {
        value: "moderate",
        top_mm: 25.4,
        left_mm: 19.1,
        bottom_mm: 25.4,
        right_mm: 19.1,
    },
     {
        value: "wide",
        top_mm: 25.4,
        left_mm: 50.8,
        bottom_mm: 25.4,
        right_mm: 50.8,
    }
])

const currentMarginDem = computed(() => {
    return marginOptions.value.find(item => item.value == margin.value) 
})
</script>
<style scoped>
@reference "~/assets/css/tailwind.css";
.margin-view {
 @apply flex relative items-center justify-center   h-[82px] w-[62px] border-2 border-gray-300 cursor-pointer text-[9px] font-bold; 
}

.margin-view.active {
    @apply border-brandPrimary/50;
}
.margin-view::after {
    @apply block  bg-gray-200/50 absolute top-0 left-0 h-full w-full  border-white; 
    z-index: 0;
    content: " ";
}

.margin-view.default::after {
    @apply border-t-8  border-b-8  border-l-[10px] border-r-[10px] ;
}

.margin-view.narrow::after {
    @apply border-5 ;
}

.margin-view.moderate::after {
    @apply border-t-8  border-b-8  border-l-[6px] border-r-[6px] ;
}

.margin-view.wide::after {
    @apply border-t-8  border-b-8  border-l-[13px] border-r-[13px] ;
}
</style>
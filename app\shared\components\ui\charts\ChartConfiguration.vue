<template>
  <div class="chart-config p-6 bg-white rounded-lg shadow-md">
    <h2 class="text-xl font-semibold mb-4">Configure Chart</h2>

    <!-- Chart Type Selection -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Chart Type</label>
      <select
        v-model="chartConfig.type"
        class="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="bar">Bar</option>
        <option value="line">Line</option>
        <option value="pie">Pie</option>
      </select>
    </div>

    <!-- Data Input -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Chart Data</label>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm text-gray-600">Labels (comma-separated)</label>
          <input
            v-model="chartConfig.labels"
            type="text"
            placeholder="e.g., Jan, Feb, Mar"
            class="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label class="text-sm text-gray-600">Values (comma-separated)</label>
          <input
            v-model="chartConfig.values"
            type="text"
            placeholder="e.g., 10, 20, 30"
            class="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>

    <!-- Styling Options -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Styling Options</label>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm text-gray-600">Background Color</label>
          <input
            v-model="chartConfig.backgroundColor"
            type="color"
            class="w-full h-10 p-1 border rounded-md"
          />
        </div>
        <div>
          <label class="text-sm text-gray-600">Border Color</label>
          <input
            v-model="chartConfig.borderColor"
            type="color"
            class="w-full h-10 p-1 border rounded-md"
          />
        </div>
      </div>
    </div>

    <!-- Chart Title -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Chart Title</label>
      <input
        v-model="chartConfig.title"
        type="text"
        placeholder="Enter chart title"
        class="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>

    <!-- Preview -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
      <div class="border p-4 rounded-md bg-gray-50">
        <canvas ref="chartPreview" height="200"></canvas>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-4">
      <button
        @click="cancel"
        class="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300"
      >
        Cancel
      </button>
      <button
        @click="insertChart"
        class="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600"
      >
        Insert Chart
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import Chart from 'chart.js/auto'

const emit = defineEmits(['insert', 'cancel'])

const chartConfig = ref({
  type: 'bar',
  labels: '',
  values: '',
  backgroundColor: '#4b9bff',
  borderColor: '#1e3a8a',
  title: ''
})

const chartPreview = ref(null)
let chartInstance = null

// Initialize chart preview
const renderChart = () => {
  if (chartInstance) {
    chartInstance.destroy()
  }

  const labels = chartConfig.value.labels.split(',').map(label => label.trim()).filter(label => label)
  const values = chartConfig.value.values.split(',').map(val => Number(val.trim())).filter(val => !isNaN(val))

  if (labels.length && values.length && labels.length === values.length) {
    chartInstance = new Chart(chartPreview.value, {
      type: chartConfig.value.type,
      data: {
        labels,
        datasets: [{
          label: chartConfig.value.title || 'Chart Data',
          data: values,
          backgroundColor: chartConfig.value.backgroundColor,
          borderColor: chartConfig.value.borderColor,
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: chartConfig.value.type !== 'pie' },
          title: { display: true, text: chartConfig.value.title || 'Preview' }
        }
      }
    })
  }
}

// Watch for changes in chart configuration to update preview
watch(chartConfig, renderChart, { deep: true })

// Initialize chart on mount
onMounted(() => {
  chartConfig.value.labels = 'Jan, Feb, Mar'
  chartConfig.value.values = '10, 20, 30'
  renderChart()
})

// Insert chart into TipTap editor
const insertChart = () => {
  const labels = chartConfig.value.labels.split(',').map(label => label.trim()).filter(label => label)
  const values = chartConfig.value.values.split(',').map(val => Number(val.trim())).filter(val => !isNaN(val))

  if (labels.length && values.length && labels.length === values.length) {
    const chartData = {
      type: chartConfig.value.type,
      labels,
      values,
      backgroundColor: chartConfig.value.backgroundColor,
      borderColor: chartConfig.value.borderColor,
      title: chartConfig.value.title
    }
    emit('insert', chartData)
  } else {
    alert('Please provide valid labels and values (same length, comma-separated).')
  }
}

// Cancel and close modal
const cancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.chart-config {
  max-width: 600px;
  margin: auto;
}
</style>
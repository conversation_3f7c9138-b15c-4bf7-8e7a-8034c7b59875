<template>
  <div v-if="editor" class="canvas-container" style="height: 100vh">
    <!-- Quick Insert Dropdown -->
    <DragHandle :editor="editor">
      <div class="flex space-x-1 align-middle justify-center bg-gray-50">
        <ui-dropdown width="40" @open="handleOpen" @close="handleClose">
          <template #trigger="{ toggle }">
            <ui-button variant="flat" size="xs" @click="toggle">
              <Icon
                name="heroicons:plus"
                size="calc(var(--spacing) * 4)"
                class="text-gray-600"
              />
            </ui-button>
          </template>
          <template #content="{ close }">
            <div class="py-2">
              <div class="flex items-center space-x-2 mb-2">
                <h3 class="text-sm font-medium text-gray-500 px-3">Insert</h3>
              </div>
              <ul>
                <li v-for="item in InsertItems" :key="item.name">
                  <ui-button
                    variant="flat"
                    color="gray"
                    :elevated="false"
                    class="w-full justify-start"
                    size="sm"
                    @click="item.command()"
                    :leading-icon="item.icon"
                  >
                    {{ item.name }}
                  </ui-button>
                </li>
              </ul>
            </div>
          </template>
        </ui-dropdown>
        <div class="custom-drag-handle" v-if="!editor.isEmpty" />
      </div>
    </DragHandle>
    <EditorContent :editor="editor" />
  </div>
</template>

<script setup lang="ts">
import '~/assets/css/list-styles.css'
import { onMounted, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import { useDocEditor } from "~/app/shared/composables/core/editors/useDocEditor";
import { DragHandle } from "@tiptap/extension-drag-handle-vue-3";
const { t } = useI18n();
const {
  editor,
  initializeEditor,
  EditorContent,
  destroyEditor,
  InsertItems,
  executeCommand,
} = useDocEditor();

const content = `
                <h1>${t("documents.editor.document.welcomeTitle")}</h1>
                <p>${t("documents.editor.document.welcomeMessage")}</p>
                <p>${t("documents.editor.document.startTyping")}</p>
              `;

const handleOpen = () => {
  editor.value?.commands?.setMeta("lockDragHandle", true);
};

const handleClose = () => {
  editor.value?.commands?.setMeta("lockDragHandle", false);
};

onMounted(() => initializeEditor(content));
onUnmounted(destroyEditor);
</script>

<style>
@reference '~/assets/css/tailwind.css';

/** reset styles */
.ProseMirror h1 {
  @apply text-3xl font-bold text-gray-900 dark:text-white mb-6;
}
.ProseMirror h2 {
  @apply text-2xl font-bold text-gray-900 dark:text-white 
  mb-6;
}
.ProseMirror h3 {
  @apply text-xl font-bold text-gray-900 dark:text-white 
  mb-6;
}
.ProseMirror h4 {
  @apply text-lg font-bold text-gray-900 dark:text-white 
  mb-6;
}
.ProseMirror h5 {
  @apply text-base font-bold text-gray-900 dark:text-white 
  mb-6;
}
.ProseMirror h6 {
  @apply text-sm font-bold text-gray-900 dark:text-white 
  mb-6;
}
.ProseMirror p {
  @apply text-base text-gray-700 dark:text-gray-300 mb-4;
}
.ProseMirror blockquote {
  @apply border-l-4 border-gray-300 dark:border-gray-600 pl-4 mb-6;
}
::selection {
  background-color: #70cff8 50;
}
.ProseMirror {
  padding: 2.54cm 3.18cm 2.54cm 3.18cm;
}
.ProseMirror * {
  margin-top: 0.75em;
}

.ProseMirror .ProseMirror-widget * {
  margin-top: auto;
}
 
.ProseMirror-noderangeselection *::selection {
  background: transparent;
}
.ProseMirror-noderangeselection * {
  caret-color: transparent;
}
.ProseMirror-selectednode,
.ProseMirror-selectednoderange {
  position: relative;
}
.ProseMirror-selectednode::before,
.ProseMirror-selectednoderange::before {
  position: absolute;
  pointer-events: none;
  z-index: -1;
  content: "";
  top: -0.25rem;
  left: -0.25rem;
  right: -0.25rem;
  bottom: -0.25rem;
  background-color: #70cff8 50;
  border-radius: 0.2rem;
}
.custom-drag-handle {
  display: flex;
  justify-content: center;
  align-items: center;
}
.custom-drag-handle::after {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1.25rem;
  content: "⠿";
  font-weight: 700;
  cursor: grab;
  background: #0d0d0d 10;
  color: var(--color-gray-600);
  border-radius: 0.25rem;
}
.ProseMirror-focused {
  outline: none;
}
.ProseMirror-focused .has-focus {
  @apply bg-brandPrimary-50/50;
}

.prosemirror-dropcursor-block {
  background-color: var(--color-brandPrimary-500) !important;
}



.ProseMirror p {
  @apply p-0;
}

/**columns */
.ProseMirror .columns-container {
  @apply grid grid-cols-(--grid-cols-count) space-x-3;
 
}
.ProseMirror .columns-container.has-focus {
  background: none;
}

.ProseMirror .columns-container .column {
 @apply bg-brandPrimary-50/50;
}
.ProseMirror .columns-container .column.has-focus {
  @apply border border-dashed border-brandPrimary-400;
} 
</style>

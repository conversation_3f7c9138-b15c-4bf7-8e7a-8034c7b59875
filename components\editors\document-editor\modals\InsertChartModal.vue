<template>
  <div class="flex w-[60rem]">
    <ui-tabs :tabs="tabs" v-model="activeTab" class="w-full">
      <template #panel-settings>
        <div class="flex space-x-12">
          <div class="flex flex-col flex-2/3">
            <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
            <div class="border p-4 rounded-sm bg-gray-50 w-full">
              <canvas ref="chartPreview" height="300" width="90%"></canvas>
            </div>
          </div>

          <div class="flex flex-col flex-1/3 space-y-4 justify-center">
            <ui-select
              label="Type"
              size="sm"
              class="w-full"
              v-model="chartConfig.type"
              id="type"
              :options="chartTypes"
            ></ui-select>
            <ui-input
              label="Tile"
              id="title"
              class="w-full"
              v-model="chartConfig.title"
              size="sm"
            />

            <ui-select
              label="Legend Position"
              size="sm"
              class="w-full"
              v-model="chartConfig.legend.position"
              id="legendPosition"
              :options="positions"
            ></ui-select>

            <!-- <ui-input label="Background Color" id="backgroundColor" class="w-10 h-10 !p-0" v-model="chartConfig.backgroundColor" size="sm" type="color" />

          <ui-input label="Border Color" id="borderColor" class="w-10 h-10 !p-0" v-model="chartConfig.borderColor" size="sm" type="color" /> -->
          </div>
        </div>
      </template>
      <template #panel-data>
        <table class="table w-full" colspan="5">
          <thead>
            <tr class="space-x-2">
              <th>{{ "Label" }}</th>
              <th>{{ "Value" }}</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><ui-input variant="flat" id="label" /></td>
              <td><ui-input variant="flat" id="value" /></td>
            </tr>
          </tbody>
        </table>
      </template>
    </ui-tabs>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, useTemplateRef, watch } from "vue";
import mitt, { Emitter, Handler } from "mitt";
import Chart from "chart.js/auto";

const emit = defineEmits(["insert", "cancel"]);
const chartTypes = ref([
  { label: "Bar Chart", value: "bar" },
  { label: "Line Chart", value: "line" },
  { label: "Pie Chart", value: "pie" },
  { label: "Doughnut Chart", value: "doughnut" },
]);

const positions = ref([
  { label: "Top", value: "top" },
  { label: "Bottom", value: "bottom" },
  { label: "Left", value: "left" },
  { label: "Right", value: "right" },
]);
const chartConfig = ref({
  type: "bar",
  labels: "a,b,c",
  values: "1,2,3",
  backgroundColor: "#4b9bff",
  borderColor: "#1e3a8a",
  title: "Chart Title",
  legend: { position: "top" },
});
const tabs = [
  { label: "Settings", value: "settings" },
  { label: "Data", value: "data" },
];
const activeTab = ref("settings");

const chartPreview = useTemplateRef<HTMLElement>("chartPreview");

let chartInstance = null;

// Initialize chart preview
const renderChart = () => {
  if (!chartPreview.value) return;
  if (chartInstance) {
    chartInstance.destroy();
  }

  const labels = chartConfig.value.labels
    .split(",")
    .map((label) => label.trim())
    .filter((label) => label);
  const values = chartConfig.value.values
    .split(",")
    .map((val) => Number(val.trim()))
    .filter((val) => !isNaN(val));

  if (labels.length && values.length && labels.length === values.length) {
    chartInstance = new Chart(chartPreview.value, {
      type: chartConfig.value.type,
      data: {
        labels,
        datasets: [
          {
            label: chartConfig.value.title || "Chart Data",
            data: values,
            backgroundColor: chartConfig.value.backgroundColor,
            borderColor: chartConfig.value.borderColor,
            borderWidth: 1,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: chartConfig.value.type !== "pie",
            position: chartConfig.value.legend.position,
          },
          title: { display: !!chartConfig.value.title, text: chartConfig.value.title },
        },
      },
    });
  }
};

// Watch for changes in chart configuration to update preview
watch(chartConfig, renderChart, { deep: true });

onMounted(renderChart);

// Extend the mitt emitter to include a 'once' method
type Events = Record<string, unknown>;
interface MittWithOnce extends Emitter<Events> {
  once: (type: string, handler: Handler) => void;
}
const emitter = mitt() as MittWithOnce;

emitter.once = function (type: string, handler: Handler) {
  const onceHandler: Handler = (event) => {
    handler(event);
    emitter.off(type, onceHandler);
  };
  emitter.on(type, onceHandler);
};

const insert = function () {};

defineExpose({
  insert,
  emitter,
});
</script>
<style scoped>
table tr > th,
table tr > td {
  padding: 0.25rem;
  border: 1px solid rgb(211, 208, 208);
}
</style>

# Tiptap Pages Extension

A sophisticated page-based editing extension for Tiptap that transforms the traditional single-block editor into a document-like interface with proper page layout, headers, footers, and page breaks.

## Features

- **Page-based Layout**: Proper margins, spacing, and visual page organization
- **Multiple Page Formats**: Built-in support for A4, A3, A5, Letter, Legal, and Tabloid formats
- **Custom Page Formats**: Create custom page sizes with specific dimensions and margins
- **Headers and Footers**: Configurable headers and footers with page numbers and custom content
- **Visual Page Breaks**: Clear visual separation between pages with manual and automatic breaks
- **Auto Page Break**: Intelligent content overflow detection and automatic page breaking
- **Document-like Experience**: Familiar interface similar to popular word processors
- **Print Support**: Optimized for printing with proper page breaks
- **Responsive Design**: Scales appropriately on different screen sizes
- **Dark Mode Support**: Built-in dark mode styling

## Installation

```typescript
import { TiptapPages } from './extensions'
import './extensions/tiptap-pages.css'

// Add to your Tiptap editor
const editor = new Editor({
  extensions: [
    StarterKit,
    TiptapPages.configure({
      format: 'A4',
      showPageNumbers: true,
      showHeaders: true,
      showFooters: true,
    }),
  ],
})
```

## Basic Usage

### Simple Setup

```typescript
import { Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { TiptapPages } from './extensions'

const editor = new Editor({
  element: document.querySelector('#editor'),
  extensions: [
    StarterKit,
    TiptapPages.configure({
      format: 'A4',
      documentTitle: 'My Document',
    }),
  ],
})
```

### With Custom Configuration

```typescript
const editor = new Editor({
  extensions: [
    StarterKit,
    TiptapPages.configure({
      format: 'Letter',
      headerHeight: 60,
      footerHeight: 60,
      pageGap: 40,
      pageBreakBackground: '#f0f9ff',
      showPageNumbers: true,
      showHeaders: true,
      showFooters: true,
      documentTitle: 'Legal Document',
    }),
  ],
})
```

## Page Formats

### Built-in Formats

| Format  | Dimensions (cm) | Dimensions (px @ 96dpi) | Margins (cm) |
|---------|-----------------|-------------------------|--------------|
| A4      | 21.0 × 29.7     | 794 × 1123             | 2.5, 2.0, 2.5, 2.0 |
| A3      | 29.7 × 42.0     | 1123 × 1587            | 2.5, 2.0, 2.5, 2.0 |
| A5      | 14.8 × 21.0     | 559 × 794              | 2.0, 1.5, 2.0, 1.5 |
| Letter  | 21.59 × 27.94   | 816 × 1063             | 2.54, 2.54, 2.54, 2.54 |
| Legal   | 21.59 × 35.56   | 816 × 1346             | 2.54, 2.54, 2.54, 2.54 |
| Tabloid | 27.94 × 43.18   | 1063 × 1634            | 2.54, 2.54, 2.54, 2.54 |

### Custom Formats

```typescript
import { createCustomFormatFromCm, createCustomFormatFromInch } from './extensions'

// Create custom format from centimeters
const customFormat = createCustomFormatFromCm(
  'Custom Legal',
  21.59, // width
  35.56, // height
  {
    top: 3.0,
    right: 2.5,
    bottom: 3.0,
    left: 2.5,
  }
)

// Create custom format from inches
const businessCard = createCustomFormatFromInch(
  'Business Card',
  3.5, // width
  2.0, // height
  {
    top: 0.2,
    right: 0.2,
    bottom: 0.2,
    left: 0.2,
  }
)

// Use in editor
editor.commands.setPageFormat(customFormat)
```

## Commands

### Page Management

```typescript
// Insert a new page
editor.commands.insertPage()

// Insert a manual page break
editor.commands.insertPageBreak()

// Set page format
editor.commands.setPageFormat('A4')
editor.commands.setPageFormat(customFormat)
```

### Document Settings

```typescript
// Set document title
editor.commands.setDocumentTitle('My Document')

// Toggle page numbers
editor.commands.togglePageNumbers()

// Toggle headers/footers
editor.commands.toggleHeaders()
editor.commands.toggleFooters()
```

### Content Management

```typescript
// Set header content
editor.commands.setHeaderContent('CONFIDENTIAL', 'right')

// Set footer content
editor.commands.setFooterContent('© 2024 Company', 'left')

// Update page layout
editor.commands.updatePageLayout({
  headerHeight: 70,
  footerHeight: 70,
  pageGap: 60,
  pageBreakBackground: '#fef3c7',
})
```

## Keyboard Shortcuts

- `Cmd/Ctrl + Enter`: Insert page break
- `Ctrl + Shift + Enter`: Insert page break

## Configuration Options

```typescript
interface PageOptions {
  format: string | CustomPageFormat          // Page format (default: 'A4')
  headerHeight?: number                      // Header height in pixels (default: 50)
  footerHeight?: number                      // Footer height in pixels (default: 50)
  pageGap?: number                          // Gap between pages (default: 50)
  pageBreakBackground?: string              // Background color between pages
  showPageNumbers?: boolean                 // Show page numbers (default: true)
  showHeaders?: boolean                     // Show headers (default: true)
  showFooters?: boolean                     // Show footers (default: true)
  documentTitle?: string                    // Document title
  customHeader?: HeaderFooterOptions        // Custom header configuration
  customFooter?: HeaderFooterOptions        // Custom footer configuration
}
```

## Utility Functions

### Measurement Conversion

```typescript
import { cmToPixels, inchToPixels, pixelsToCm, pixelsToInch } from './extensions'

// Convert measurements
const widthPx = cmToPixels(21.0)      // Convert cm to pixels
const heightPx = inchToPixels(11)     // Convert inches to pixels
const widthCm = pixelsToCm(794)       // Convert pixels to cm
const heightInch = pixelsToInch(1123) // Convert pixels to inches
```

### Page Calculations

```typescript
import { calculatePageDimensions, generatePageCSS } from './extensions'

// Calculate page dimensions
const dimensions = calculatePageDimensions('A4', 50, 50, 50)
console.log(dimensions.contentWidth, dimensions.contentHeight)

// Generate CSS for a format
const css = generatePageCSS('A4', {
  headerHeight: 60,
  footerHeight: 60,
  pageGap: 40,
})
```

## Styling

### CSS Classes

- `.tiptap-pages-container`: Main container
- `.tiptap-page`: Individual page
- `.tiptap-page-header`: Page header
- `.tiptap-page-content`: Page content area
- `.tiptap-page-footer`: Page footer
- `.tiptap-page-break`: Page break indicator

### CSS Variables

```css
:root {
  --page-width: 794px;
  --page-height: 1123px;
  --content-width: 754px;
  --content-height: 1023px;
  --page-margin-top: 20px;
  --page-margin-right: 20px;
  --page-margin-bottom: 20px;
  --page-margin-left: 20px;
  --header-height: 50px;
  --footer-height: 50px;
  --page-gap: 50px;
  --page-break-background: #f6f3f4;
}
```

### Custom Styling

```css
.tiptap-pages-container {
  background: #f8fafc;
}

.tiptap-page {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.tiptap-page-header {
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  font-weight: 600;
}
```

## Events and Storage

### Accessing Storage

```typescript
// Get current page count
const pageCount = editor.storage.tiptapPages?.pageCount

// Get current format
const format = editor.storage.tiptapPages?.format

// Get document title
const title = editor.storage.tiptapPages?.documentTitle
```

### Event Handling

```typescript
editor.on('update', () => {
  const storage = editor.storage.tiptapPages
  console.log('Pages:', storage?.pageCount)
  console.log('Format:', storage?.format)
})
```

## Advanced Features

### Auto Page Break

The extension automatically detects content overflow and can trigger page breaks:

```typescript
// Enable auto page break
editor.storage.tiptapPages.autoPageBreak = true

// Listen for overflow
editor.on('update', () => {
  let hasOverflow = false
  editor.state.doc.descendants((node) => {
    if (node.type.name === 'pageContent' && node.attrs.overflow) {
      hasOverflow = true
    }
  })
  
  if (hasOverflow) {
    console.log('Content overflow detected!')
  }
})
```

### Print Optimization

The extension includes print-specific CSS that:
- Removes shadows and backgrounds
- Ensures proper page breaks
- Hides overflow indicators
- Optimizes for print media

## Browser Support

- Chrome/Chromium 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## License

MIT License - see LICENSE file for details.

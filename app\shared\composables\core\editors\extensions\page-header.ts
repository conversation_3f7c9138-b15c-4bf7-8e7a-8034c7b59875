import { Node, mergeAttributes } from '@tiptap/core'
import type { PageHeaderAttributes } from './types'

export const PageHeader = Node.create({
  name: 'pageHeader',
  
  group: 'block',
  content: 'text*',
  isolating: true,
  draggable: false,
  selectable: true,

  addAttributes() {
    return {
      pageNumber: {
        default: 1,
        parseHTML: element => parseInt(element.getAttribute('data-page-number') || '1'),
        renderHTML: attributes => ({
          'data-page-number': attributes.pageNumber,
        }),
      },
      documentTitle: {
        default: null,
        parseHTML: element => element.getAttribute('data-document-title'),
        renderHTML: attributes => {
          if (attributes.documentTitle) {
            return { 'data-document-title': attributes.documentTitle }
          }
          return {}
        },
      },
      customContent: {
        default: null,
        parseHTML: element => element.getAttribute('data-custom-content'),
        renderHTML: attributes => {
          if (attributes.customContent) {
            return { 'data-custom-content': attributes.customContent }
          }
          return {}
        },
      },
      alignment: {
        default: 'center',
        parseHTML: element => element.getAttribute('data-alignment') || 'center',
        renderHTML: attributes => ({
          'data-alignment': attributes.alignment,
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="page-header"]',
        priority: 100,
      },
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    const attrs = node.attrs as PageHeaderAttributes
    
    return [
      'div',
      mergeAttributes(HTMLAttributes, {
        'data-type': 'page-header',
        'class': `tiptap-page-header tiptap-page-header-${attrs.alignment}`,
        'style': `
          height: var(--header-height, 50px);
          padding: 8px var(--page-margin-right, 20px) 8px var(--page-margin-left, 20px);
          border-bottom: 1px solid #e5e7eb;
          background: #f9fafb;
          display: flex;
          align-items: center;
          justify-content: ${attrs.alignment === 'left' ? 'flex-start' : attrs.alignment === 'right' ? 'flex-end' : 'center'};
          font-size: 12px;
          color: #6b7280;
          box-sizing: border-box;
        `,
      }),
      0,
    ]
  },

  addNodeView() {
    return ({ node, HTMLAttributes, getPos, editor }) => {
      const dom = document.createElement('div')
      const contentDOM = document.createElement('div')
      
      const attrs = node.attrs as PageHeaderAttributes
      
      // Set up the container
      Object.assign(dom, {
        ...HTMLAttributes,
        'data-type': 'page-header',
        className: `tiptap-page-header tiptap-page-header-${attrs.alignment}`,
      })
      
      // Apply styles
      dom.style.cssText = `
        height: var(--header-height, 50px);
        padding: 8px var(--page-margin-right, 20px) 8px var(--page-margin-left, 20px);
        border-bottom: 1px solid #e5e7eb;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: ${attrs.alignment === 'left' ? 'flex-start' : attrs.alignment === 'right' ? 'flex-end' : 'center'};
        font-size: 12px;
        color: #6b7280;
        box-sizing: border-box;
        position: relative;
      `
      
      // Create content areas
      const leftContent = document.createElement('div')
      const centerContent = document.createElement('div')
      const rightContent = document.createElement('div')
      
      leftContent.style.cssText = 'flex: 1; text-align: left;'
      centerContent.style.cssText = 'flex: 1; text-align: center;'
      rightContent.style.cssText = 'flex: 1; text-align: right;'
      
      // Set content based on attributes and custom content
      const documentTitle = attrs.documentTitle || editor.storage.tiptapPages?.documentTitle || ''
      const customContent = attrs.customContent || ''
      
      if (customContent) {
        // Use custom content
        if (attrs.alignment === 'left') {
          leftContent.textContent = customContent
        } else if (attrs.alignment === 'right') {
          rightContent.textContent = customContent
        } else {
          centerContent.textContent = customContent
        }
      } else if (documentTitle) {
        // Use document title
        if (attrs.alignment === 'left') {
          leftContent.textContent = documentTitle
        } else if (attrs.alignment === 'right') {
          rightContent.textContent = documentTitle
        } else {
          centerContent.textContent = documentTitle
        }
      }
      
      // Add editable content area
      contentDOM.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 8px var(--page-margin-right, 20px) 8px var(--page-margin-left, 20px);
        display: flex;
        align-items: center;
        justify-content: ${attrs.alignment === 'left' ? 'flex-start' : attrs.alignment === 'right' ? 'flex-end' : 'center'};
        background: transparent;
        z-index: 1;
      `
      
      // If there's no custom content, show the default content
      if (!node.textContent && !customContent) {
        dom.appendChild(leftContent)
        dom.appendChild(centerContent)
        dom.appendChild(rightContent)
      }
      
      dom.appendChild(contentDOM)

      return {
        dom,
        contentDOM,
      }
    }
  },

  addCommands() {
    return {
      setHeaderContent: (content: string, alignment: 'left' | 'center' | 'right' = 'center') => ({ commands, state }) => {
        const { selection } = state
        const { $from } = selection
        
        // Find the current page
        let pagePos: number | null = null
        let headerPos: number | null = null
        
        state.doc.descendants((node, pos) => {
          if (node.type.name === 'page') {
            // Check if this page contains the current selection
            if (pos <= $from.pos && $from.pos <= pos + node.nodeSize) {
              pagePos = pos
              
              // Find the header within this page
              node.descendants((childNode, childPos) => {
                if (childNode.type.name === 'pageHeader') {
                  headerPos = pos + childPos + 1
                  return false
                }
              })
              return false
            }
          }
        })
        
        if (headerPos !== null) {
          return commands.updateAttributes('pageHeader', {
            customContent: content,
            alignment,
          })
        }
        
        return false
      },
    }
  },
})

// Tiptap Pages Extension - Main Export
export { TiptapPages } from './tiptap-pages'
export { PageNode } from './page-node'
export { PageHeader } from './page-header'
export { PageFooter } from './page-footer'
export { PageBreak } from './page-break'
export { PageContent } from './page-content'

// Types and interfaces
export type {
  PageFormat,
  PageOptions,
  PageLayoutOptions,
  HeaderFooterOptions,
  CustomPageFormat
} from './types'

// Utilities
export {
  cmToPixels,
  inchToPixels,
  getPageFormat,
  createCustomFormat,
  calculatePageDimensions
} from './utils'

// Built-in page formats
export { PAGE_FORMATS } from './formats'

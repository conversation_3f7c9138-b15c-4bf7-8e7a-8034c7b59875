<template>
  <div :class="['editor-secondary-panel', { opened }]">
   
  </div>
</template>
<script setup lang="ts">
interface Props{
 opened?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  opened: false
})
</script>
<style scoped>
@reference '~/assets/css/tailwind.css';
.editor-secondary-panel {
    @apply border-s-2 border-gray-200 w-0 bg-gray-50  flex flex-col items-start p-0 shadow-md absolute right-0 top-0 h-full z-10  transition-all ease-in-out duration-300;
}
.editor-secondary-panel.opened {
  @apply w-[250px] px-4 py-2;
}
</style>

<template>
  <div class="editor-test-page min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header Controls -->
    <div class="sticky top-0 z-50 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
              Tiptap Pages Editor Test
            </h1>
            <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <span>Pages: {{ pageCount }}</span>
              <span>•</span>
              <span>Format: {{ currentFormat }}</span>
            </div>
          </div>

          <!-- Toolbar -->
          <div class="flex items-center space-x-2">
            <!-- Page Format Selector -->
            <select
              v-model="selectedFormat"
              @change="changeFormat"
              class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="A4">A4</option>
              <option value="A3">A3</option>
              <option value="A5">A5</option>
              <option value="Letter">Letter</option>
              <option value="Legal">Legal</option>
              <option value="Tabloid">Tabloid</option>
            </select>

            <!-- Document Title Input -->
            <input
              v-model="documentTitle"
              @input="updateDocumentTitle"
              placeholder="Document Title"
              class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white w-48"
            />

            <!-- Toggle Buttons -->
            <button
              @click="togglePageNumbers"
              :class="[
                'px-3 py-1 text-sm rounded-md transition-colors',
                showPageNumbers
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              ]"
            >
              Page #
            </button>

            <button
              @click="toggleHeaders"
              :class="[
                'px-3 py-1 text-sm rounded-md transition-colors',
                showHeaders
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              ]"
            >
              Headers
            </button>

            <button
              @click="toggleFooters"
              :class="[
                'px-3 py-1 text-sm rounded-md transition-colors',
                showFooters
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              ]"
            >
              Footers
            </button>

            <!-- Action Buttons -->
            <button
              @click="insertPage"
              class="px-3 py-1 text-sm bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-md hover:bg-green-200 dark:hover:bg-green-800 transition-colors"
            >
              + Page
            </button>

            <button
              @click="insertPageBreak"
              class="px-3 py-1 text-sm bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300 rounded-md hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors"
            >
              Page Break
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Editor Container -->
    <div class="tiptap-pages-container" ref="editorContainer">
      <EditorContent  :editor="editor" ref="editorElement" class="tiptap-pages-editor" />
    </div>

    <!-- Floating Panel -->
    <div class="fixed bottom-6 right-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 w-80">
      <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Page Settings</h3>

      <!-- Header Content -->
      <div class="mb-3">
        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Header Content
        </label>
        <div class="flex space-x-2">
          <input
            v-model="headerContent"
            placeholder="Header text"
            class="flex-1 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <select
            v-model="headerAlignment"
            class="px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
          </select>
          <button
            @click="updateHeaderContent"
            class="px-2 py-1 text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800"
          >
            Set
          </button>
        </div>
      </div>

      <!-- Footer Content -->
      <div class="mb-3">
        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Footer Content
        </label>
        <div class="flex space-x-2">
          <input
            v-model="footerContent"
            placeholder="Footer text"
            class="flex-1 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <select
            v-model="footerAlignment"
            class="px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
          </select>
          <button
            @click="updateFooterContent"
            class="px-2 py-1 text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800"
          >
            Set
          </button>
        </div>
      </div>

      <!-- Layout Settings -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <label class="text-xs font-medium text-gray-700 dark:text-gray-300">
            Header Height
          </label>
          <input
            v-model.number="headerHeight"
            @input="updateLayout"
            type="number"
            min="30"
            max="100"
            class="w-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div class="flex items-center justify-between">
          <label class="text-xs font-medium text-gray-700 dark:text-gray-300">
            Footer Height
          </label>
          <input
            v-model.number="footerHeight"
            @input="updateLayout"
            type="number"
            min="30"
            max="100"
            class="w-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div class="flex items-center justify-between">
          <label class="text-xs font-medium text-gray-700 dark:text-gray-300">
            Page Gap
          </label>
          <input
            v-model.number="pageGap"
            @input="updateLayout"
            type="number"
            min="20"
            max="100"
            class="w-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      <!-- Keyboard Shortcuts Info -->
      <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
        <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Keyboard Shortcuts</h4>
        <div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
          <div class="flex justify-between">
            <span>Page Break:</span>
            <span class="font-mono">Cmd/Ctrl + Enter</span>
          </div>
          <div class="flex justify-between">
            <span>Alt Page Break:</span>
            <span class="font-mono">Ctrl + Shift + Enter</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { Focus, CharacterCount  } from "@tiptap/extensions";
import { TextStyle, FontFamily, FontSize, LineHeight } from "@tiptap/extension-text-style";
import TextAlign from '@tiptap/extension-text-align'
import Image from '@tiptap/extension-image'

// Import our new Tiptap Pages extension
import { TiptapPages } from '~/app/shared/composables/core/editors/extensions'

definePageMeta({
  layout: "canvas",
})

// Template refs
const editorElement = ref<HTMLElement>()
const editorContainer = ref<HTMLElement>()

// Editor instance
let editor: Editor | null = null

// Reactive state
const pageCount = ref(0)
const currentFormat = ref('A4')
const selectedFormat = ref('A4')
const documentTitle = ref('Legal Document Template')
const showPageNumbers = ref(true)
const showHeaders = ref(true)
const showFooters = ref(true)

// Content settings
const headerContent = ref('')
const headerAlignment = ref<'left' | 'center' | 'right'>('center')
const footerContent = ref('')
const footerAlignment = ref<'left' | 'center' | 'right'>('center')

// Layout settings
const headerHeight = ref(50)
const footerHeight = ref(50)
const pageGap = ref(50)

// Initialize editor
onMounted(() => {
  if (!editorElement.value) return

  // Add the pages container class
  if (editorContainer.value) {
    editorContainer.value.classList.add('tiptap-pages-container')
  }

  const content = {
      type: 'doc',
      content: [
        {
          type: 'page',
          content: [
            {
              type: 'paragraph',
              content: [
                { type: 'text', text: '' },
              ],
            },
          ],
        },
      ],
    }
  // Initialize editor with Tiptap Pages extension

  editor = new Editor({
    element: editorElement.value,
    extensions: [
      StarterKit.configure({
        // Disable default document structure since we're using pages
        document: false,
      }),
      Focus,
      CharacterCount,
      TextStyle,
      FontFamily,
      FontSize,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      LineHeight,
      Image,
      TiptapPages.configure({
        format: selectedFormat.value,
        headerHeight: headerHeight.value,
        footerHeight: footerHeight.value,
        pageGap: pageGap.value,
        pageBreakBackground: '#f6f3f4',
        showPageNumbers: showPageNumbers.value,
        showHeaders: showHeaders.value,
        showFooters: showFooters.value,
        documentTitle: documentTitle.value,
      }),
    ],
    content: content,
    onCreate: ({ editor }) => {
      console.log('Tiptap Pages editor created')
      updatePageCount()
    },
    onUpdate: ({ editor }) => {
      updatePageCount()
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
      },
    },
  })

  // Update page count initially
  updatePageCount()
})

// Cleanup
onUnmounted(() => {
  if (editor) {
    editor.destroy()
  }
})

// Helper functions
const updatePageCount = () => {
  if (editor?.storage.tiptapPages) {
    pageCount.value = editor.storage.tiptapPages.pageCount || 0
  }
}

// Command functions
const changeFormat = () => {
  if (editor) {
    editor.commands.setPageFormat(selectedFormat.value)
    currentFormat.value = selectedFormat.value
  }
}

const updateDocumentTitle = () => {
  if (editor) {
    editor.commands.setDocumentTitle(documentTitle.value)
  }
}

const togglePageNumbers = () => {
  showPageNumbers.value = !showPageNumbers.value
  if (editor) {
    editor.commands.togglePageNumbers()
  }
}

const toggleHeaders = () => {
  showHeaders.value = !showHeaders.value
  if (editor) {
    editor.commands.toggleHeaders()
  }
}

const toggleFooters = () => {
  showFooters.value = !showFooters.value
  if (editor) {
    editor.commands.toggleFooters()
  }
}

const insertPage = () => {
  if (editor) {
    editor.commands.insertPage()
  }
}

const insertPageBreak = () => {
  if (editor) {
    editor.commands.insertPageBreak()
  }
}

const updateHeaderContent = () => {
  if (editor && headerContent.value) {
    editor.commands.setHeaderContent(headerContent.value, headerAlignment.value)
  }
}

const updateFooterContent = () => {
  if (editor && footerContent.value) {
    editor.commands.setFooterContent(footerContent.value, footerAlignment.value)
  }
}

const updateLayout = () => {
  if (editor) {
    editor.commands.updatePageLayout({
      headerHeight: headerHeight.value,
      footerHeight: footerHeight.value,
      pageGap: pageGap.value,
    })
  }
}

// Watch for document title changes
watch(documentTitle, (newTitle) => {
  if (editor) {
    editor.commands.setDocumentTitle(newTitle)
  }
})
</script>

<style scoped>
/* Import the Tiptap Pages CSS */
@import '~/app/shared/composables/core/editors/extensions/tiptap-pages.css';

/* Additional custom styles for the test page */
.editor-test-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.tiptap-pages-container {
  background: #f6f3f4;
  padding: 2rem 1rem;
  min-height: calc(100vh - 4rem);
}

.tiptap-pages-editor {
  outline: none;
}

/* Enhanced page styling for the test */
.tiptap-pages-container .tiptap-page {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.3s ease;
}

.tiptap-pages-container .tiptap-page:hover {
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15), 0 10px 20px -5px rgba(0, 0, 0, 0.08);
}

/* Custom header and footer styling */
.tiptap-pages-container .tiptap-page-header {
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  border-bottom: 2px solid #e5e7eb;
}

.tiptap-pages-container .tiptap-page-footer {
  background: linear-gradient(to right, #f3f4f6, #f9fafb);
  border-top: 2px solid #e5e7eb;
}

/* Enhanced page break styling */
.tiptap-pages-container .tiptap-page-break {
  background: linear-gradient(135deg, #f6f3f4 0%, #f0f0f0 100%);
}

.tiptap-pages-container .tiptap-page-break-indicator {
  background: white;
  border: 2px dashed #d1d5db;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Floating panel styling */
.fixed.bottom-6.right-6 {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.dark .fixed.bottom-6.right-6 {
  background: rgba(31, 41, 55, 0.95);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .fixed.bottom-6.right-6 {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
    width: auto;
  }
}

/* Focus styles for better accessibility */
.tiptap-pages-editor:focus-within .tiptap-page-content {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .sticky,
  .fixed {
    display: none !important;
  }

  .tiptap-pages-container {
    background: white;
    padding: 0;
  }
}
</style>
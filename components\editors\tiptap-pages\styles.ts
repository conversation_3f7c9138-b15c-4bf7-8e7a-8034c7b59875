import { pageFormats } from './formats'

export function generatePagesCSS(options: any) {
  const format = pageFormats[options.pageFormat] || options.pageFormat

  return `
    .ProseMirror {
      padding: 0 !important;
      background: ${options.pageBreakBackground};
    }

    .ProseMirror-content {
      padding: ${options.pageGap}px 0;
    }

    .page {
      background: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e5e5;
      margin: 0 auto 50px auto;
      position: relative;
      width: ${format.width}px;
      height: ${format.height}px;
      padding: ${format.margins.top}px ${format.margins.right}px ${format.margins.bottom}px ${format.margins.left}px;

    }

    .page::before {
      content: 'Page ' attr(data-page-number);
      position: absolute;
      top: -30px;
      left: 50%;
      transform: translateX(-50%);
      background: #f0f0f0;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      color: #333;
    }

    .page-header, .page-footer {
      position: absolute;
      left: ${format.margins.left}px;
      right: ${format.margins.right}px;
      height: ${options.headerHeight}px;
      z-index: 1;
    }

    .page-header {
      top: 0;
      height: ${options.headerHeight}px;
    }

    .page-footer {
      bottom: 0;
      height: ${options.footerHeight}px;
    }
  `
}
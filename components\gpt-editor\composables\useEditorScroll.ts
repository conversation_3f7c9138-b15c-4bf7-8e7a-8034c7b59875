import { shallowRef, onMounted, onUnmounted, useTemplateRef, watch } from "vue";
import { useScroll, useElementBounding } from "@vueuse/core";
import { useDocumentEditorStore } from "../../../stores/documentEditor";

export const useEditorScroll = (el: string) => {
    const editorDocStore = useDocumentEditorStore()
    const elRef = useTemplateRef<HTMLElement>(el);

    const { y: scrollY } = useScroll(elRef, {
        behavior: "smooth"
    });
     

    const { x, y, top, right, bottom, left, width, height } = useElementBounding(elRef);

    watch([x, y, top, right, bottom, left, width, height], ([x, y, top, right, bottom, left, width, height]) => {
        editorDocStore.setRect({ x, y, top, right, bottom, left, width, height })
    }, { immediate: true })

    const scrollHeight = shallowRef(0)
    const interval = shallowRef<ReturnType<typeof setInterval> | null>(null)

    onMounted(() => {
        interval.value = setInterval(() => {
            scrollHeight.value = elRef.value?.scrollHeight ?? 0
        }, 100);
    })
    onUnmounted(() => {
        if (interval.value !== null) {
            clearInterval(interval.value);
        }
    })

    watch(scrollHeight, (sh) => {
        editorDocStore.setScrollHeight(sh);
    }, { immediate: true})
 
    watch(() => editorDocStore.y, (nval, oval) => {
        scrollY.value = nval
        
    })
    // watch(scrollY, (v) => {


    //     console.log(v, editorDocStore.hasScroll, editorDocStore.scrollArea)
    // })


    return { scrollY }
}
<template>
  <ui-image-upload @change="handleUpload"></ui-image-upload>
</template>
<script setup lang="ts">
import mitt, { Emitter, Hand<PERSON>} from 'mitt';

// Extend the mitt emitter to include a 'once' method
type Events = Record<string, unknown>;
interface MittWithOnce extends Emitter<Events> {
  once: (type: string, handler: Handler) => void;
}
const emitter = mitt() as MittWithOnce;

emitter.once = function (type: string, handler: Handler) {
  const onceHandler: Handler = (event) => {
    handler(event);
    emitter.off(type, onceHandler);
  };
  emitter.on(type, onceHandler);
};
const fileURL = ref(null)
const handleUpload = (file) => {
 
  fileURL.value = URL.createObjectURL(file);
  
}
const getFile = function() { emitter.emit('change', fileURL.value)}
defineExpose({
  getFile,

  emitter
})
</script>
<template>
  <div class="space-y-4">
    <ui-select
      id="chartType"
      v-model="selectedChartType"
      :options="chartTypes"
      label="Chart Type"
      placeholder="Select a chart type"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import mitt, { Emitter, Handler } from 'mitt';
import UiSelect from '../UiSelect.vue';

// Extend the mitt emitter to include a 'once' method
type Events = Record<string, unknown>;
interface MittWithOnce extends Emitter<Events> {
  once: (type: string, handler: Handler) => void;
}
const emitter = mitt() as MittWithOnce;

emitter.once = function (type: string, handler: Handler) {
  const onceHandler: Handler = (event) => {
    handler(event);
    emitter.off(type, onceHandler);
  };
  emitter.on(type, onceHandler);
};

const selectedChartType = ref('');
const chartData = ref({});

const chartTypes = ref([
  { label: 'Area Chart', value: 'area' },
  { label: 'Bar Chart', value: 'bar' },
  { label: 'Line Chart', value: 'line' },
  { label: 'Donut Chart', value: 'donut' },
]);

const insert = function () {
  emitter.emit('insert', {
    type: selectedChartType.value,
    data: chartData.value,
  });
};

defineExpose({
  insert,
  emitter,
});
</script>
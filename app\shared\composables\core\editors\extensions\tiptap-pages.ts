import { Extension } from '@tiptap/core'
import { <PERSON>lugin, Plugin<PERSON><PERSON> } from 'prosemirror-state'
import { PageNode } from './page-node'
import { PageContent } from './page-content'
import { PageHeader } from './page-header'
import { PageFooter } from './page-footer'
import { PageBreak } from './page-break'
import { generatePageCSS, getPageFormat, debounce } from './utils'
import type { PageOptions, CustomPageFormat } from './types'

export const TiptapPages = Extension.create<PageOptions>({
  name: 'tiptapPages',

  addOptions() {
    return {
      format: 'A4',
      headerHeight: 50,
      footerHeight: 50,
      pageGap: 50,
      pageBreakBackground: '#f6f3f4',
      showPageNumbers: true,
      showHeaders: true,
      showFooters: true,
      documentTitle: null,
      customHeader: undefined,
      customFooter: undefined,
    }
  },

  addStorage() {
    return {
      format: this.options.format,
      headerHeight: this.options.headerHeight,
      footerHeight: this.options.footerHeight,
      pageGap: this.options.pageGap,
      pageBreakBackground: this.options.pageBreakBackground,
      showPageNumbers: this.options.showPageNumbers,
      showHeaders: this.options.showHeaders,
      showFooters: this.options.showFooters,
      documentTitle: this.options.documentTitle,
      customHeader: this.options.customHeader,
      customFooter: this.options.customFooter,
      autoPageBreak: true,
      pageCount: 0,
    }
  },

  addExtensions() {
    return [
      PageNode,
      PageContent,
      PageHeader,
      PageFooter,
      PageBreak,
    ]
  },

  addCommands() {
    return {
      setPageFormat: (format: string | CustomPageFormat) => ({ editor }) => {
        const storage = editor.storage.tiptapPages
        storage.format = format
        
        // Update CSS variables
        this.updatePageCSS(editor)
        
        // Update all existing pages
        const tr = editor.state.tr
        let hasChanges = false
        
        editor.state.doc.descendants((node, pos) => {
          if (node.type.name === 'page') {
            const formatName = typeof format === 'string' ? format : format.name
            if (node.attrs.format !== formatName) {
              tr.setNodeMarkup(pos, undefined, {
                ...node.attrs,
                format: formatName,
              })
              hasChanges = true
            }
          }
        })
        
        if (hasChanges) {
          editor.view.dispatch(tr)
        }
        
        return true
      },

      setDocumentTitle: (title: string) => ({ editor }) => {
        const storage = editor.storage.tiptapPages
        storage.documentTitle = title
        
        // Update all headers and footers
        const tr = editor.state.tr
        let hasChanges = false
        
        editor.state.doc.descendants((node, pos) => {
          if (node.type.name === 'pageHeader' || node.type.name === 'pageFooter') {
            if (node.attrs.documentTitle !== title) {
              tr.setNodeMarkup(pos, undefined, {
                ...node.attrs,
                documentTitle: title,
              })
              hasChanges = true
            }
          }
        })
        
        if (hasChanges) {
          editor.view.dispatch(tr)
        }
        
        return true
      },

      togglePageNumbers: () => ({ editor }) => {
        const storage = editor.storage.tiptapPages
        storage.showPageNumbers = !storage.showPageNumbers
        
        // Update all footers
        const tr = editor.state.tr
        let hasChanges = false
        
        editor.state.doc.descendants((node, pos) => {
          if (node.type.name === 'pageFooter') {
            if (node.attrs.showPageNumbers !== storage.showPageNumbers) {
              tr.setNodeMarkup(pos, undefined, {
                ...node.attrs,
                showPageNumbers: storage.showPageNumbers,
              })
              hasChanges = true
            }
          }
        })
        
        if (hasChanges) {
          editor.view.dispatch(tr)
        }
        
        return true
      },

      toggleHeaders: () => ({ editor }) => {
        const storage = editor.storage.tiptapPages
        storage.showHeaders = !storage.showHeaders
        
        // This would require more complex logic to add/remove headers
        // For now, just update the storage
        return true
      },

      toggleFooters: () => ({ editor }) => {
        const storage = editor.storage.tiptapPages
        storage.showFooters = !storage.showFooters
        
        // This would require more complex logic to add/remove footers
        // For now, just update the storage
        return true
      },

      updatePageLayout: (options) => ({ editor }) => {
        const storage = editor.storage.tiptapPages
        
        Object.assign(storage, options)
        
        // Update CSS variables
        this.updatePageCSS(editor)
        
        return true
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('tiptap-pages'),
        
        view: (editorView) => {
          // Initialize CSS
          this.updatePageCSS(this.editor)
          
          // Auto page break handler
          const debouncedPageBreak = debounce(() => {
            this.handleAutoPageBreak(editorView)
          }, 300)
          
          // Observe content changes
          const observer = new MutationObserver(() => {
            if (this.editor.storage.tiptapPages?.autoPageBreak) {
              debouncedPageBreak()
            }
          })
          
          observer.observe(editorView.dom, {
            childList: true,
            subtree: true,
            characterData: true,
          })
          
          return {
            destroy() {
              observer.disconnect()
            },
          }
        },
        
        state: {
          init: () => ({}),
          apply: (tr, pluginState) => {
            // Update page count
            let pageCount = 0
            tr.doc.descendants((node) => {
              if (node.type.name === 'page') {
                pageCount++
              }
            })
            
            if (this.editor.storage.tiptapPages) {
              this.editor.storage.tiptapPages.pageCount = pageCount
            }
            
            return pluginState
          },
        },
      }),
    ]
  },

  onCreate({ editor }) {
    // Initialize with first page if empty
    if (editor.isEmpty) {
      editor.commands.insertPage()
    }
  },

  onUpdate({ editor }) {
    // Update page numbers
    this.updatePageNumbers(editor)
  },

  // Helper methods
  updatePageCSS(editor: any) {
    const storage = editor.storage.tiptapPages
    if (!storage) return
    
    const cssText = generatePageCSS(storage.format, {
      headerHeight: storage.headerHeight,
      footerHeight: storage.footerHeight,
      pageGap: storage.pageGap,
      pageBreakBackground: storage.pageBreakBackground,
    })
    
    // Apply CSS to editor container
    const editorElement = editor.view.dom.closest('.tiptap-pages-container') || editor.view.dom
    if (editorElement) {
      editorElement.style.cssText += cssText
    }
  },

  updatePageNumbers(editor: any) {
    let pageNumber = 1
    const tr = editor.state.tr
    let hasChanges = false

    editor.state.doc.descendants((node: any, pos: number) => {
      if (node.type.name === 'page') {
        if (node.attrs.pageNumber !== pageNumber) {
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            pageNumber,
          })
          hasChanges = true
        }
        
        // Update child headers and footers
        node.descendants((childNode: any, childPos: number) => {
          if (childNode.type.name === 'pageHeader' || childNode.type.name === 'pageFooter') {
            if (childNode.attrs.pageNumber !== pageNumber) {
              tr.setNodeMarkup(pos + childPos + 1, undefined, {
                ...childNode.attrs,
                pageNumber,
              })
              hasChanges = true
            }
          }
        })
        
        pageNumber++
      }
    })

    if (hasChanges) {
      editor.view.dispatch(tr)
    }
  },

  handleAutoPageBreak(editorView: any) {
    // This would implement automatic page breaking logic
    // For now, it's a placeholder for future implementation
    console.log('Auto page break check')
  },
})

// stores/notificationTrigger.ts
import { defineStore } from 'pinia';



export const useDocumentEditorStore = defineStore('documentEditor', {
    state: () => ({
        //content scroll
        y: 0,
        scrollHeight: 0,
        rect: {
            bottom: 0,
            height: 0,
            left: 0,
            right : 0,
            top: 0,
            width: 0,
            x: 0,
            y: 0
        }
    }),

    getters: {
        height: ({rect}) => rect.height,
        scrollArea({ scrollHeight  }){ return scrollHeight - this.height },
        hasScroll(){ return this.scrollArea > 0 },
        percentage () {
            return (100 * this.scrollArea) / this.scrollHeight
        },
        handleHeight () {
            return (100 - this.percentage) + "%"
        }
    },

    actions: {
        setY(y) {
            this.y = y || 0
        },
 
        setScrollHeight(sh) {
            this.scrollHeight = sh || 0
        },
 
        setRect({ bottom, height, left, right, top, width, x, y }) {
            this.rect.bottom = bottom
             this.rect.height = height
             this.rect.left = left
             this.rect.right = right
             this.rect.top =top
             this.rect.width = width
             this.rect.x = x
             this.rect.y = y
        }

    },
});
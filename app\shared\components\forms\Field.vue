<template>

  <component
    :is="resolvedComponent"
    v-if="showIf"
    :id="props.name"
    :name="props.name"
    :label="fieldLabel"
    v-model="value"
    v-bind="fieldProps"
    v-on="fieldEvents"
    class="mt-1"
    :class="{ 'border-red-500': !meta.valid && meta.touched }"
    :error-message="errorMessage"
    :error="errorMessage"
  >
    <template v-for="(slot, slotName) in props.slots" :key="slotName" #[slotName]>
      {{ slot }}
    </template>
  </component>
</template>

<script setup lang="ts">
import { useField, type InputType } from "vee-validate";
import { computed } from "vue";
import { toTypedSchema } from '@vee-validate/zod'
import { useFormsProps } from "~/app/shared/composables/useFormsProps";

interface Props {
  name: string;
  label?: string | Function;
  component: string | any;
  rules?: any;
  props?: any;
  events?: any;
  showIf?: boolean | Function;
  initialValue?: any;
  slots?: any;
}

interface FieldOptions {
  label?: string;
  type?: InputType;
  initialValue?: any;
  validateOnMount?: boolean;
  bails?: boolean;
  standalone?: boolean;
  validateOnValueUpdate?: boolean;
  keepValueOnUnmount?: boolean;
  syncVModel?: boolean;
  checkedValue?: any; // (checkbox/radio only)
  uncheckedValue?: any; // (checkbox/radio only)
}

import UiInput from "../ui/UiInput.vue";
import UiTextarea from "../ui/UiTextarea.vue";
import UiSelect from "../ui/UiSelect.vue";
import UiCheckbox from "../ui/UiCheckbox.vue";
import UiSlugInput from "../ui/UiSlugInput.vue";
import UiLogoUpload from "../ui/UiLogoUpload.vue";
import UiEmailsInput from "../ui/UiEmailsInput.vue";
import UiInputHidden from "../ui/UiInputHidden.vue";
 

const componentMap = {
  UiInput,
  UiTextarea,
  UiSelect,
  UiCheckbox,
  UiSlugInput,
  UiLogoUpload,
  UiEmailsInput,
  UiInputHidden,
};

type ComponentMapKeys = keyof typeof componentMap;

const props = withDefaults(defineProps<Props>(), {
  name: "",
  label: "",
  component: "UiInputHidden",
  props: () => ({}),
  events: () => ({}),
  showIf: () => true,
  rules: () => ({}),
  initialValue: () => null,

});
 
 
 
 

const { props: fieldProps, label: fieldLabel, events: fieldEvents, showIf } = useFormsProps(props);

  
const fieldOptions: FieldOptions = {
  label: props.label as string,
  initialValue: props.initialValue,
}
if (fieldProps.value?.type) {
  fieldOptions.type = fieldProps.value?.type as InputType;
}

const { value, errorMessage, meta } = useField(props.name, toTypedSchema(props.rules), fieldOptions);
 
 

const resolvedComponent = computed(() => {
  if (typeof props.component === "string") {
    return componentMap[props.component as ComponentMapKeys];
  }
 
  
  return props.component;
});



 


</script>

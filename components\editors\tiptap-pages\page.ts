import { extensions, Node } from '@tiptap/core'
import { calcContentHeight } from './utils';

export const PageConten = Node.create({
  name: 'content',
  group: 'block',
  content: 'block+',
  isolating: true,
  draggable: false,


  parseHTML() {
    return [
      {
        tag: 'div[data-type="content"].page-content',
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', { ...HTMLAttributes, 'data-type': 'page-content', class: 'page-content' }, 0]
  },
})
export const Page = Node.create({
  name: 'page',
  group: 'block',
  content: 'header? content? footer?',
  isolating: true,
  draggable: false,

  addExtensions() {
    return [PageConten]
  },


  addAttributes() {
    return {
      'data-page-number': {
        default: null,
        renderHTML: (attributes) => {
          if (!attributes['data-page-number']) {
            return {};
          }

          return {
            'data-page-number': attributes['data-page-number'],
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="page"].page',
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', { ...HTMLAttributes, 'data-type': 'page', class: 'page' }, 0]
  },
  onUpdate({ editor }) {
    const isEditorFocused = editor.isFocused;
    let sub = 0;
    const el = editor.view.domAtPos(editor.state.selection.$from.pos);
    const pageEl = (el.node as HTMLElement).closest('div[data-type="page"]')
    const pageHeaderEl = pageEl?.querySelector('div[data-type="page-header"]')
    const pageContentEl = pageEl?.querySelector('div[data-type="page-content"]')
    const pageFooterEl = pageEl?.querySelector('div[data-type="page-footer"]')

    const pageHeight = pageEl?.clientHeight
    const pageHeadeHeight = pageHeaderEl?.clientHeight
    const pageContentHeight = pageContentEl?.clientHeight
    const pageFooterHeight = pageFooterEl?.clientHeight

    // console.log({ pageHeight, pageHeadeHeight, pageContentHeight, pageFooterHeight });

    console.log(editor.storage.pages.format);
    

    
    // const limit = (pageEl?.clientHeight ?? 0) - (pageHeadeHeight + pageFooterHeight)
    // const contentHeight = pageHeight - (pageHeadeHeight + pageFooterHeight)
    // console.log(contentHeight > limit, contentHeight, limit);

  }

})
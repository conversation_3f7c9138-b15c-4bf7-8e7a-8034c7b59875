import { Extension } from '@tiptap/core'

import './types'
import { pageFormats } from './formats.ts'
import { calcContentHeight } from './utils'
import { generatePagesCSS } from './styles.ts'
import { Page } from './page.ts'
import { Header } from './header.ts'
import { Footer } from './footer.ts'




export const Pages = Extension.create({
  name: 'pages',
  addExtensions() {
    return [Page, Header, Footer];
  },
  addStorage() {
    return {
      format: this.options.pageFormat,
      settings: pageFormats[this.options.pageFormat]
    }
  },
  addOptions() {
    return {
      format: 'A4',
      margins: pageFormats.A4.margins,
      headerHeight: 50,
      footerHeight: 50,
      pageGap: 50,
      pageBreakBackground: '#f6f3f4',
    }
  },

  addProseMirrorPlugins() {
    return [
      
    ]
  },

  onBeforeCreate() {
    const { options } = this
  
    
    const css = generatePagesCSS(options)
    const style = document.createElement('style');
    style.setAttribute('data-tiptap-pages-style', '');
    style.innerHTML = css;
    document.head.appendChild(style);
  },

  addCommands() {
    return {
      setPageFormat: (format) => ({editor}) => {
     
        
        this.storage.format = format
        this.storage.settings = pageFormats[format]
        this.options.format = format;
        this.options.margins =  pageFormats[format].margins;
        editor.commands.setMeta('page-format', format)
        
        return true
      },
    };
  },

  onUpdate() {
    const { tr } = this.editor.state;
    let pageNumber = 1;
    this.editor.state.doc.forEach((node, pos) => {
      if (node.type.name === 'page') {
        if (node.attrs['data-page-number'] !== pageNumber) {
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            'data-page-number': pageNumber,
          });
        }
        pageNumber++;
      
        
      }
    });
    if (tr.docChanged) {
      this.editor.view.dispatch(tr);
    }
  },

  onCreate() {
    
    const pageNode = {
      type: 'page',
      attrs: {},
      content: [
        {
        type: 'header',
        attrs: {},
        content: [],
      },
        {
        type: 'content',
        attrs: {},
        content: [{
        type: 'paragraph',
        attrs: {},
        content: [],
      },],
      },
      {
        type: 'footer',
        attrs: {},
        content: [],
      }
    ],
    };


     this.editor.commands.insertContent([pageNode,pageNode]);
    //  this.editor.commands.insertContent(pageNode);
  }
})

/* Custom List Styles */

/* ===== BULLET LIST TYPES ===== */

/* 1. Filled Circle */
.list-bullet-filled-circle {
  list-style: none;
  padding-left: 0;
}

.list-bullet-filled-circle li {
  margin-bottom: 0.5rem;
}

.list-bullet-filled-circle li p {
  position: relative;
  padding-left: .5rem;
  margin: 0;
}

.list-bullet-filled-circle li p::before {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 0.5px);
  width: 5px;
  height: 5px;
  background-color: currentColor;
  border-radius: 50%;
}

/* 2. Opened Circle */
.list-bullet-open-circle {
  list-style: none;
  padding-left: 0;
}

.list-bullet-open-circle li {
  margin-bottom: 0.5rem;
}

.list-bullet-open-circle li p {
  position: relative;
  padding-left: .5rem;
  margin: 0;
}

.list-bullet-open-circle li p::before {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 0.5px);
  width: 5px;
  height: 5px;
  border: 1px solid currentColor;
  border-radius: 50%;
  background-color: transparent;
}

/* 3. Filled Rectangle */
.list-bullet-filled-rect {
  list-style: none;
  padding-left: 0;
}

.list-bullet-filled-rect li {
  margin-bottom: 0.5rem;
}

.list-bullet-filled-rect li p {
  position: relative;
  padding-left: .5rem;
  margin: 0;
}

.list-bullet-filled-rect li p::before {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 0.5px);
  width: 5px;
  height: 5px;
  background-color: currentColor;
}

/* 4. Opened Rectangle */
.list-bullet-open-rect {
  list-style: none;
  padding-left: 0;
}

.list-bullet-open-rect li {
  margin-bottom: 0.5rem;
}

.list-bullet-open-rect li p {
  position: relative;
  padding-left: .5rem;
  margin: 0;
}

.list-bullet-open-rect li p::before {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 0.5px);
  width: 5px;
  height: 5px;
  border: 1px solid currentColor;
  background-color: transparent;
}

/* ===== ORDERED LIST TYPES ===== */

/* 1. Numbers (1, 2, 3...) */
.list-ordered-numbers {
  list-style: none;
  padding-left: 0;
  counter-reset: numbers-counter;
}

.list-ordered-numbers li {
  margin-bottom: 0.5rem;
  counter-increment: numbers-counter;
}

.list-ordered-numbers li p {
  position: relative;
  padding-left: 1rem;
  margin: 0;
}

.list-ordered-numbers li p::before {
  content: counter(numbers-counter, decimal) ". ";
  position: absolute;
  left: 0;
 
}

/* 2. Leading Zero Numbers (01, 02, 03...) */
.list-ordered-leading-zero {
  list-style: none;
  padding-left: 0;
  counter-reset: leading-zero-counter;
}

.list-ordered-leading-zero li {
  margin-bottom: 0.5rem;
  counter-increment: leading-zero-counter;
}

.list-ordered-leading-zero li p {
  position: relative;
  padding-left: 1rem;
  margin: 0;
}

.list-ordered-leading-zero li p::before {
  content: counter(leading-zero-counter, decimal-leading-zero) ". ";
  position: absolute;
  left: 0;
 
}

/* 3. Alpha Upper (A, B, C...) */
.list-ordered-alpha-upper {
  list-style: none;
  padding-left: 0;
  counter-reset: alpha-upper-counter;
}

.list-ordered-alpha-upper li {
  margin-bottom: 0.5rem;
  counter-increment: alpha-upper-counter;
}

.list-ordered-alpha-upper li p {
  position: relative;
  padding-left: 1rem;
  margin: 0;
}

.list-ordered-alpha-upper li p::before {
  content: counter(alpha-upper-counter, upper-alpha) ". ";
  position: absolute;
  left: 0;
 
}

/* 4. Alpha Lower (a, b, c...) */
.list-ordered-alpha-lower {
  list-style: none;
  padding-left: 0;
  counter-reset: alpha-lower-counter;
}

.list-ordered-alpha-lower li {
  margin-bottom: 0.5rem;
  counter-increment: alpha-lower-counter;
}

.list-ordered-alpha-lower li p {
  position: relative;
  padding-left: 1rem;
  margin: 0;
}

.list-ordered-alpha-lower li p::before {
  content: counter(alpha-lower-counter, lower-alpha) ". ";
  position: absolute;
  left: 0;
 
}

/* 5. Roman Upper (I, II, III...) */
.list-ordered-roman-upper {
  list-style: none;
  padding-left: 0;
  counter-reset: roman-upper-counter;
}

.list-ordered-roman-upper li {
  margin-bottom: 0.5rem;
  counter-increment: roman-upper-counter;
}

.list-ordered-roman-upper li p {
  position: relative;
  padding-left: 1rem;
  margin: 0;
}

.list-ordered-roman-upper li p::before {
  content: counter(roman-upper-counter, upper-roman) ". ";
  position: absolute;
  left: 0;
 
}

/* 6. Roman Lower (i, ii, iii...) */
.list-ordered-roman-lower {
  list-style: none;
  padding-left: 0;
  counter-reset: roman-lower-counter;
}

.list-ordered-roman-lower li {
  margin-bottom: 0.5rem;
  counter-increment: roman-lower-counter;
}

.list-ordered-roman-lower li p {
  position: relative;
  padding-left: 1rem;
  margin: 0;
}

.list-ordered-roman-lower li p::before {
  content: counter(roman-lower-counter, lower-roman) ". ";
  position: absolute;
  left: 0;
 
}

/* 7. Hebrew Alpha (א, ב, ג...) */
.list-ordered-hebrew {
  list-style: none;
  padding-left: 0;
  counter-reset: hebrew-counter;
  direction: rtl;
}

.list-ordered-hebrew li {
  margin-bottom: 0.5rem;
  counter-increment: hebrew-counter;
}

.list-ordered-hebrew li p {
  position: relative;
   padding-right: 1rem;
  margin: 0;
}

.list-ordered-hebrew li p::before {
  content: counter(hebrew-counter, hebrew) ". ";
  position: absolute;
  right: 0;
 
}

/* 8. Arabic Alpha (أ, ب, ت...) */
.list-ordered-arabic {
  list-style: none;
  padding-left: 0;
  counter-reset: arabic-counter;
  direction: rtl;
}

.list-ordered-arabic li {
  margin-bottom: 0.5rem;
  counter-increment: arabic-counter;
}

.list-ordered-arabic li p {
  position: relative;
   padding-right: 1rem;
  margin: 0;
}

.list-ordered-arabic li:nth-child(1) p::before {
  content: "أ. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(2) p::before {
  content: "ب. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(3) p::before {
  content: "ت. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(4) p::before {
  content: "ث. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(5) p::before {
  content: "ج. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(6) p::before {
  content: "ح. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(7) p::before {
  content: "خ. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(8) p::before {
  content: "د. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(9) p::before {
  content: "ذ. ";
  position: absolute;
  right: 0;
 
}

.list-ordered-arabic li:nth-child(10) p::before {
  content: "ر. ";
  position: absolute;
  right: 0;
 
}

/* ===== SUB-LISTS (NESTED LISTS) ===== */

/* Nested bullet lists - use different bullet styles for each level */
.list-bullet-filled-circle ul,
.list-bullet-open-circle ul,
.list-bullet-filled-rect ul,
.list-bullet-open-rect ul {
  list-style: none;
  padding-left: 0;
  margin-top: 0.5rem;
  margin-left: .5rem; /* Indent sub-lists */
}

.list-bullet-filled-circle ul li,
.list-bullet-open-circle ul li,
.list-bullet-filled-rect ul li,
.list-bullet-open-rect ul li {
  margin-bottom: 0.25rem;
}

.list-bullet-filled-circle ul li p,
.list-bullet-open-circle ul li p,
.list-bullet-filled-rect ul li p,
.list-bullet-open-rect ul li p {
  padding-left: .5rem;
  position: relative;
  margin: 0;
}

/* Filled Circle → Open Circle */
.list-bullet-filled-circle ul li p::before {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 0.5px);
  width: 6px;
  height: 6px;
  border: 1px solid currentColor;
  border-radius: 50%;
  background-color: transparent;
}

/* Open Circle → Filled Circle */
.list-bullet-open-circle ul li p::before {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 0.5px);
  width: 6px;
  height: 6px;
  background-color: currentColor;
  border-radius: 50%;
}

/* Filled Rectangle → Open Rectangle */
.list-bullet-filled-rect ul li p::before {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 0.5px);
  width: 6px;
  height: 6px;
  border: 1px solid currentColor;
  background-color: transparent;
}

/* Open Rectangle → Filled Rectangle */
.list-bullet-open-rect ul li p::before {
  content: '';
  position: absolute;
  left: 0;
  top: calc(50% - 0.5px);
  width: 6px;
  height: 6px;
  background-color: currentColor;
}

/* Nested ordered lists - hierarchical numbering (1.1, 1.2, 2.1, 2.2, etc.) */
.list-ordered-numbers ol,
.list-ordered-leading-zero ol,
.list-ordered-alpha-upper ol,
.list-ordered-alpha-lower ol,
.list-ordered-roman-upper ol,
.list-ordered-roman-lower ol,
.list-ordered-hebrew ol,
.list-ordered-arabic ol {
  list-style: none;
  padding-left: 0;
  margin-top: 0.5rem;
  margin-left: 2rem; /* Indent sub-lists */
  counter-reset: sub-counter;
}
.list-ordered-hebrew ol,
.list-ordered-arabic ol {
    margin-left: 0; /* Indent sub-lists */
    margin-right: 2rem; /* Indent sub-lists */
}

.list-ordered-numbers ol li,
.list-ordered-leading-zero ol li,
.list-ordered-alpha-upper ol li,
.list-ordered-alpha-lower ol li,
.list-ordered-roman-upper ol li,
.list-ordered-roman-lower ol li,
.list-ordered-hebrew ol li,
.list-ordered-arabic ol li {
  counter-increment: sub-counter;
  margin-bottom: 0.25rem;
}

.list-ordered-numbers ol li p,
.list-ordered-leading-zero ol li p,
.list-ordered-alpha-upper ol li p,
.list-ordered-alpha-lower ol li p,
.list-ordered-roman-upper ol li p,
.list-ordered-roman-lower ol li p {
  padding-left: 3rem;
  position: relative;
  margin: 0;
}

/* Numbers: 1.1, 1.2, 2.1, 2.2 */
.list-ordered-numbers ol li p::before {
  content: counter(numbers-counter) "." counter(sub-counter) ". ";
  position: absolute;
  left: 0;
 
}

/* Leading Zero: 01.01, 01.02, 02.01, 02.02 */
.list-ordered-leading-zero ol li p::before {
  content: counter(leading-zero-counter, decimal-leading-zero) "." counter(sub-counter, decimal-leading-zero) ". ";
  position: absolute;
  left: 0;
 
}

/* Alpha Upper: A.A, A.B, B.A, B.B */
.list-ordered-alpha-upper ol li p::before {
  content: counter(alpha-upper-counter, upper-alpha) "." counter(sub-counter, upper-alpha) ". ";
  position: absolute;
  left: 0;
 
}

/* Alpha Lower: a.a, a.b, b.a, b.b */
.list-ordered-alpha-lower ol li p::before {
  content: counter(alpha-lower-counter, lower-alpha) "." counter(sub-counter, lower-alpha) ". ";
  position: absolute;
  left: 0;
 
}

/* Roman Upper: I.I, I.II, II.I, II.II */
.list-ordered-roman-upper ol li p::before {
  content: counter(roman-upper-counter, upper-roman) "." counter(sub-counter, upper-roman) ". ";
  position: absolute;
  left: 0;
 
}

/* Roman Lower: i.i, i.ii, ii.i, ii.ii */
.list-ordered-roman-lower ol li p::before {
  content: counter(roman-lower-counter, lower-roman) "." counter(sub-counter, lower-roman) ". ";
  position: absolute;
  left: 0;
 
}

/* Hebrew and Arabic sub-lists */
.list-ordered-hebrew ol li p,
.list-ordered-arabic ol li p {
  padding-right: 3rem;
  padding-left: 0;
  position: relative;
  margin: 0;
}

/* Hebrew: א.א, א.ב, ב.א, ב.ב */
.list-ordered-hebrew ol li p::before {
  content: counter(hebrew-counter, hebrew) "." counter(sub-counter, hebrew) ". ";
  position: absolute;
  right: 0;
  left: auto;
 
}

/* Arabic: أ.أ, أ.ب, ب.أ, ب.ب */
/* We need to create specific rules for each parent-child combination */

/* First parent (أ) sub-items */
.list-ordered-arabic li:nth-child(1) ol li:nth-child(1) p::before {
  content: "أ.أ. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

.list-ordered-arabic li:nth-child(1) ol li:nth-child(2) p::before {
  content: "أ.ب. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

.list-ordered-arabic li:nth-child(1) ol li:nth-child(3) p::before {
  content: "أ.ت. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

/* Second parent (ب) sub-items */
.list-ordered-arabic li:nth-child(2) ol li:nth-child(1) p::before {
  content: "ب.أ. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

.list-ordered-arabic li:nth-child(2) ol li:nth-child(2) p::before {
  content: "ب.ب. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

.list-ordered-arabic li:nth-child(2) ol li:nth-child(3) p::before {
  content: "ب.ت. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

/* Third parent (ت) sub-items */
.list-ordered-arabic li:nth-child(3) ol li:nth-child(1) p::before {
  content: "ت.أ. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

.list-ordered-arabic li:nth-child(3) ol li:nth-child(2) p::before {
  content: "ت.ب. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

.list-ordered-arabic li:nth-child(3) ol li:nth-child(3) p::before {
  content: "ت.ت. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

/* Fourth parent (ث) sub-items */
.list-ordered-arabic li:nth-child(4) ol li:nth-child(1) p::before {
  content: "ث.أ. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

.list-ordered-arabic li:nth-child(4) ol li:nth-child(2) p::before {
  content: "ث.ب. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

.list-ordered-arabic li:nth-child(4) ol li:nth-child(3) p::before {
  content: "ث.ت. ";
  position: absolute;
  right: 0;
  left: auto;
 
}

/* ===== UTILITY CLASSES ===== */

/* Color variants for bullets */
.list-bullet-primary li p::before {
  color: #3b82f6;
}

.list-bullet-success li p::before {
  color: #10b981;
}

.list-bullet-warning li p::before {
  color: #f59e0b;
}

.list-bullet-danger li p::before {
  color: #ef4444;
}

/* Size variants */
.list-bullet-sm li p::before {
  width: 6px;
  height: 6px;
  top: 0.7rem;
}

.list-bullet-lg li p::before {
  width: 10px;
  height: 10px;
  top: 0.5rem;
}

/* Spacing variants */
.list-compact li {
  margin-bottom: 0.25rem;
}

.list-spacious li {
  margin-bottom: 1rem;
}

/* RTL Support */
.rtl .list-bullet-filled-circle li p,
.rtl .list-bullet-open-circle li p,
.rtl .list-bullet-filled-rect li p,
.rtl .list-bullet-open-rect li p {
  padding-left: 0;
  padding-right: 1.5rem;
}

.rtl .list-bullet-filled-circle li p::before,
.rtl .list-bullet-open-circle li p::before,
.rtl .list-bullet-filled-rect li p::before,
.rtl .list-bullet-open-rect li p::before {
  left: auto;
  right: 0;
}

.rtl .list-ordered-numbers li p,
.rtl .list-ordered-leading-zero li p,
.rtl .list-ordered-alpha-upper li p,
.rtl .list-ordered-alpha-lower li p,
.rtl .list-ordered-roman-upper li p,
.rtl .list-ordered-roman-lower li p {
  padding-left: 0;
   padding-right: 1rem;
  direction: rtl;
}

.rtl .list-ordered-numbers li p::before,
.rtl .list-ordered-leading-zero li p::before,
.rtl .list-ordered-alpha-upper li p::before,
.rtl .list-ordered-alpha-lower li p::before,
.rtl .list-ordered-roman-upper li p::before,
.rtl .list-ordered-roman-lower li p::before {
  left: auto;
  right: 0;
}

/* RTL support for sub-lists */
.rtl .list-bullet-filled-circle ul,
.rtl .list-bullet-open-circle ul,
.rtl .list-bullet-filled-rect ul,
.rtl .list-bullet-open-rect ul {
  margin-left: 0;
  margin-right: 1.5rem;
}

.rtl .list-ordered-numbers ol,
.rtl .list-ordered-leading-zero ol,
.rtl .list-ordered-alpha-upper ol,
.rtl .list-ordered-alpha-lower ol,
.rtl .list-ordered-roman-upper ol,
.rtl .list-ordered-roman-lower ol,
.rtl .list-ordered-hebrew ol,
.rtl .list-ordered-arabic ol {
  margin-left: 0;
  margin-right: 2rem;
}

.rtl .list-ordered-numbers ol li p,
.rtl .list-ordered-leading-zero ol li p,
.rtl .list-ordered-alpha-upper ol li p,
.rtl .list-ordered-alpha-lower ol li p,
.rtl .list-ordered-roman-upper ol li p,
.rtl .list-ordered-roman-lower ol li p {
  padding-left: 0;
  padding-right: 3rem;
}

.rtl .list-ordered-numbers ol li p::before,
.rtl .list-ordered-leading-zero ol li p::before,
.rtl .list-ordered-alpha-upper ol li p::before,
.rtl .list-ordered-alpha-lower ol li p::before,
.rtl .list-ordered-roman-upper ol li p::before,
.rtl .list-ordered-roman-lower ol li p::before {
  left: auto;
  right: 0;
}

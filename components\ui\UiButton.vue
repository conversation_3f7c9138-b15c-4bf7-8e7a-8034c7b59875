<template>
  <component
    :is="componentTag"
    ref="buttonRef"
    :type="componentTag === 'button' || 'submit' ? props.type : undefined"
    :href="componentTag === 'a' ? props.href : undefined"
    :to="componentTag === 'NuxtLink' ? props.to : undefined"
    :disabled="props.disabled || props.loading"
    :aria-label="props.ariaLabel"
    :aria-describedby="props.ariaDescribedby"
    :class="[
      'relative inline-flex items-center justify-center font-medium transition-all duration-200 ease-in-out ',
      'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
      currentSizeClasses,
      shapeClasses,
      currentVariantClasses,
      disabledClasses,
      interactiveClasses,
      props.fullWidth ? 'w-full' : '',
      props.elevated ? 'shadow-lg hover:shadow-xl' : '',
      props.shape === 'circle' && !props.loading ? 'p-0' : ''
    ]"
    @click="handleClick"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseLeave"
  >
    <!-- Ripple Effect -->
    <span
      v-if="showRipple && rippleStyle"
      :class="[
        'absolute rounded-full bg-white/30 pointer-events-none',
        'animate-ripple'
      ]"
      :style="rippleStyle"
    />

    <!-- Loading State -->
    <template v-if="props.loading">
      <UiSpinner
        :size="spinnerSize"
        :class="[
          'text-current',
          ($slots.default && props.shape !== 'circle') ? 'mr-2' : ''
        ]"
      />
      <span v-if="props.shape !== 'circle' && ($slots.default || props.loadingText)">
        {{ props.loadingText || 'Loading...' }}
      </span>
    </template>

    <!-- Normal State -->
    <template v-else>
      <!-- Leading Icon -->
 
      <Icon
        v-if="props.leadingIcon"
        :name="props.leadingIcon"
    
        :style="`font-size: calc(var(--spacing) * ${getMultiplier});`"
      
      />
      
       
    

      <!-- Left Icon Slot -->
      <span v-if="$slots['icon-left']" :class="[$slots.default ? 'mr-2' : '']">
        <slot name="icon-left" />
      </span>

      <!-- Button Content -->
      <span v-if="$slots.default" class="truncate">
        <slot />
      </span>

      <!-- Right Icon Slot -->
      <span v-if="$slots['icon-right']" :class="[$slots.default ? 'ml-2' : '']">
        <slot name="icon-right" />
      </span>

      <!-- Trailing Icon -->
      <Icon
        v-if="props.trailingIcon"
        :name="props.trailingIcon"
        :size="iconSizeClasses"
        :class="[
          $slots.default ? 'ml-2' : ''
        ]"
      />
    </template>

    <!-- Badge/Notification Dot -->
    <span
      v-if="props.badge"
      :class="[
        'absolute -top-1 -right-1 h-3 w-3 rounded-full',
        typeof props.badge === 'string' ? 'bg-red-500' : 'bg-red-500',
        'ring-2 ring-white dark:ring-gray-800'
      ]"
    >
      <span v-if="typeof props.badge === 'string'" class="sr-only">{{ props.badge }}</span>
    </span>
  </component>
</template>

<script setup lang="ts">
import { computed, withDefaults, ref, onMounted, onUnmounted, nextTick } from 'vue';
import UiSpinner from './UiSpinner.vue';
import { get } from 'lodash-es';

interface Props {
  type?: 'button' | 'submit' | 'reset';
  variant?: 'contained' | 'outline' | 'ghost' | 'flat' | 'gradient' | 'primary' | 'danger' | 'secondary';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'gray' | 'blue';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'default' | 'pill' | 'circle' | 'square';
  disabled?: boolean;
  loading?: boolean;
  loadingText?: string;
  to?: string;
  href?: string;
  leadingIcon?: string;
  trailingIcon?: string;
  ariaLabel?: string;
  ariaDescribedby?: string;
  fullWidth?: boolean;
  elevated?: boolean;
  ripple?: boolean;
  badge?: boolean | string | number;
}

interface Emits {
  (e: 'click', event: MouseEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'button',
  variant: 'contained',
  color: 'primary',
  size: 'md',
  shape: 'default',
  disabled: false,
  loading: false,
  fullWidth: false,
  elevated: false,
  ripple: true,
});

const emit = defineEmits<Emits>();

const buttonRef = ref<HTMLElement | null>(null);
const showRipple = ref(false);
const rippleStyle = ref<Record<string, string> | null>(null);

// Component tag logic
const componentTag = computed(() => {
  if (props.to) return 'NuxtLink';
  if (props.href) return 'a';
  return 'button';
});

// Enhanced computed properties
const iconSizeClasses = computed(() => {
  const sizeMap = {
    xs: 'calc(var(spacing) * 3)',
    sm: 'calc(var(spacing) *  4.5)',
    md: 'calc(var(spacing) * 5)',
    lg: 'calc(var(spacing) * 6)',
    xl: 'calc(var(spacing) * 7)'
  };
  return (sizeMap[props.size] || sizeMap.md )as string;
});


const getMultiplier = computed((): number => {
  const multiplierMap = {
    xs: 3,
    sm: 4.5,
    md: 5,
    lg: 6,
    xl: 7
  };
  return multiplierMap[props.size] || multiplierMap.md;
});

const spinnerSize = computed((): 'xs' | 'sm' | 'md' | 'lg' | 'xl' => {
  const sizeMap: Record<string, 'xs' | 'sm' | 'md' | 'lg' | 'xl'> = {
    xs: 'xs',
    sm: 'sm',
    md: 'sm',
    lg: 'md',
    xl: 'lg'
  };
  return sizeMap[props.size] || 'sm';
});

const disabledClasses = computed(() => {
  if (props.disabled || props.loading) {
    return 'opacity-50 cursor-not-allowed pointer-events-none';
  }
  return '';
});

const interactiveClasses = computed(() => {
  if (props.disabled || props.loading) return '';

  const baseClasses = 'cursor-pointer select-none';
  const hoverClasses = props.elevated
    ? 'hover:scale-105 active:scale-95'
    : 'hover:scale-[1.02] active:scale-[0.98]';

  return `${baseClasses} ${hoverClasses}`;
});

// Methods for ripple effect and interactions
const handleClick = (event: MouseEvent) => {

 
  
  if (props.disabled || props.loading) return;
  emit('click', event);
};

const handleMouseDown = (event: MouseEvent) => {
  if (!props.ripple || props.disabled || props.loading) return;

  const button = event.currentTarget as HTMLElement;
  const rect = button.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = event.clientX - rect.left - size / 2;
  const y = event.clientY - rect.top - size / 2;

  rippleStyle.value = {
    width: `${size}px`,
    height: `${size}px`,
    left: `${x}px`,
    top: `${y}px`,
  };

  showRipple.value = true;

  // Auto-hide ripple after animation
  setTimeout(() => {
    showRipple.value = false;
    rippleStyle.value = null;
  }, 600);
};

const handleMouseUp = () => {
  // Additional logic if needed
};

const handleMouseLeave = () => {
  // Hide ripple when mouse leaves
  showRipple.value = false;
  rippleStyle.value = null;
};


// Enhanced size classes with better spacing and typography
const currentSizeClasses = computed(() => {
  if (props.shape === 'circle') {
    const circleMap = {
      xs: 'w-6 h-6 text-xs',
      sm: 'w-8 h-8 text-sm',
      md: 'w-10 h-10 text-sm',
      lg: 'w-12 h-12 text-base',
      xl: 'w-14 h-14 text-lg'
    };
    return circleMap[props.size] || circleMap.md;
  }

  const sizeMap = {
    xs: 'px-2 py-1 text-xs gap-1',
    sm: 'px-3 py-1.5 text-sm gap-1.5',
    md: 'px-4 py-2 text-sm gap-2',
    lg: 'px-5 py-2.5 text-base gap-2',
    xl: 'px-6 py-3 text-base gap-2.5'
  };
  return sizeMap[props.size] || sizeMap.md;
});

const shapeClasses = computed(() => {
  const shapeMap = {
    default: 'rounded-md',
    pill: 'rounded-full',
    circle: 'rounded-full',
    square: 'rounded-none'
  };
  return shapeMap[props.shape] || shapeMap.default;
});

const currentVariantClasses = computed(() => {
  const transition = 'transition-all duration-200 ease-in-out';

  // Color mappings for different color options
  const colorMap = {
    primary: {
      bg: 'bg-brandPrimary',
      bgHover: 'hover:bg-brandPrimary-dark',
      bgLight: 'bg-brandPrimary-100',
      bgLightHover: 'hover:bg-brandPrimary-200',
      text: 'text-brandPrimary',
      textLight: 'text-brandPrimary-800',
      textOnBg: 'text-white',
      border: 'border-brandPrimary',
      focusRing: 'focus-visible:ring-brandPrimary/50'
    },
    secondary: {
      bg: 'bg-brandSecondary',
      bgHover: 'hover:bg-brandSecondary-dark',
      bgLight: 'bg-brandSecondary-100',
      bgLightHover: 'hover:bg-brandSecondary-200',
      text: 'text-brandSecondary',
      textLight: 'text-brandSecondary-800',
      textOnBg: 'text-white',
      border: 'border-brandSecondary',
      focusRing: 'focus-visible:ring-brandSecondary/50'
    },
    success: {
      bg: 'bg-green-600',
      bgHover: 'hover:bg-green-700',
      bgLight: 'bg-green-100',
      bgLightHover: 'hover:bg-green-200',
      text: 'text-green-600',
      textLight: 'text-green-800',
      textOnBg: 'text-white',
      border: 'border-green-600',
      focusRing: 'focus-visible:ring-green-500/50'
    },
    warning: {
      bg: 'bg-yellow-500',
      bgHover: 'hover:bg-yellow-600',
      bgLight: 'bg-yellow-100',
      bgLightHover: 'hover:bg-yellow-200',
      text: 'text-yellow-600',
      textLight: 'text-yellow-800',
      textOnBg: 'text-white',
      border: 'border-yellow-500',
      focusRing: 'focus-visible:ring-yellow-500/50'
    },
    danger: {
      bg: 'bg-brandDanger',
      bgHover: 'hover:bg-brandDanger-dark',
      bgLight: 'bg-red-100',
      bgLightHover: 'hover:bg-red-200',
      text: 'text-brandDanger',
      textLight: 'text-red-800',
      textOnBg: 'text-white',
      border: 'border-brandDanger',
      focusRing: 'focus-visible:ring-brandDanger/50'
    },
    info: {
      bg: 'bg-blue-600',
      bgHover: 'hover:bg-blue-700',
      bgLight: 'bg-blue-100',
      bgLightHover: 'hover:bg-blue-200',
      text: 'text-blue-600',
      textLight: 'text-blue-800',
      textOnBg: 'text-white',
      border: 'border-blue-600',
      focusRing: 'focus-visible:ring-blue-500/50'
    },
    gray: {
      bg: 'bg-gray-600',
      bgHover: 'hover:bg-gray-700',
      bgLight: 'bg-gray-100',
      bgLightHover: 'hover:bg-gray-200',
      text: 'text-gray-600',
      textLight: 'text-gray-800',
      textOnBg: 'text-white',
      border: 'border-gray-600',
      focusRing: 'focus-visible:ring-gray-500/50'
    },
    blue: {
      bg: 'bg-blue-600',
      bgHover: 'hover:bg-blue-700',
      bgLight: 'bg-blue-100',
      bgLightHover: 'hover:bg-blue-200',
      text: 'text-blue-600',
      textLight: 'text-blue-800',
      textOnBg: 'text-white',
      border: 'border-blue-600',
      focusRing: 'focus-visible:ring-blue-500/50'
    }
  };

  const colors = colorMap[props.color] || colorMap.primary;
  const focusRing = `focus-visible:ring-2 focus-visible:ring-offset-2 ${colors.focusRing}`;

  // Gradient classes based on color
  const gradientClasses = {
    primary: 'bg-gradient-to-r from-brandPrimary to-brandPrimary-600 hover:from-brandPrimary-dark hover:to-brandPrimary-700',
    secondary: 'bg-gradient-to-r from-brandSecondary to-brandSecondary-600 hover:from-brandSecondary-dark hover:to-brandSecondary-700',
    success: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800',
    warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700',
    danger: 'bg-gradient-to-r from-brandDanger to-red-600 hover:from-brandDanger-dark hover:to-red-700',
    info: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800',
    gray: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800',
    blue: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800'
  };

  const variantMap = {
    contained: `${colors.bg} ${colors.textOnBg} ${colors.bgHover} hover:shadow-md border border-transparent ${focusRing} ${transition}`,
    outline: `bg-transparent ${colors.text} border ${colors.border} ${colors.bgHover} hover:${colors.textOnBg} hover:shadow-md ${focusRing} ${transition}`,
    ghost: `bg-transparent ${colors.text} hover:${colors.bgLight} dark:hover:bg-gray-800  hover:bg-gray-100 border border-transparent ${focusRing} ${transition}`,
    flat: `bg-transparent ${colors.text} hover:${colors.bgLight} dark:hover:bg-gray-800 hover:bg-gray-100  border border-transparent ${focusRing} ${transition}`,
    gradient: `${gradientClasses[props.color] || gradientClasses.primary} ${colors.textOnBg} hover:shadow-lg border border-transparent ${focusRing} ${transition}`,
    primary: `${colors.bg} ${colors.textOnBg} ${colors.bgHover} hover:shadow-md border border-transparent ${focusRing} ${transition}`,
    danger: `${colors.bg} ${colors.textOnBg} ${colors.bgHover} hover:shadow-md border border-transparent ${focusRing} ${transition}`,
    secondary: `bg-transparent ${colors.text} border ${colors.border} ${colors.bgHover} hover:${colors.textOnBg} hover:shadow-md ${focusRing} ${transition}`
  };

  return variantMap[props.variant] || variantMap.contained;
});

</script>

<style scoped>
/* Enhanced button animations and effects */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.animate-ripple {
  animation: ripple 0.6s ease-out;
}

/* Enhanced focus states for better accessibility */
.focus-visible\:ring-2:focus-visible {
  outline: none;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

/* Smooth transitions for all interactive states */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Enhanced hover effects */
.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-ripple {
    animation: none;
  }

  .transition-all {
    transition: none;
  }

  .hover\:scale-105:hover,
  .hover\:scale-\[1\.02\]:hover,
  .active\:scale-95:active,
  .active\:scale-\[0\.98\]:active {
    transform: none;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .focus-visible\:ring-2:focus-visible {
    --tw-ring-offset-color: #1f2937;
  }
}
</style>
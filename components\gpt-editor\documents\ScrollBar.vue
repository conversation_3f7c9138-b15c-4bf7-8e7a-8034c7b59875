<template>
  <div
    class="scroll-wraper transition-all ease-in-out duration-300 z-50"
    :style="{ right: (panelOpened ? 255 : 20) + 'px' }"
  >
    <div class="scroll-arrow" @click="scrollT">
      <Icon name="typcn:chevron-left" class="font-bold rotate-90" />
    </div>
    <div class="scroll-holder">
      <div
        class="scroll-handle"
        ref="handleRef"
        :class="{ pressed }"
        :style="style"
        @mousedown.prevent.stop
      ></div>
    </div>
    <div class="scroll-arrow" @click="scrollB">
      <Icon name="typcn:chevron-left" class="font-bold rotate-270" />
    </div>
  </div>
</template>
<script setup lang="ts">
interface Props {
  panelOpened?: boolean;
}
import { clamp, useMousePressed, useMouse } from "@vueuse/core";
import { useTemplateRef, watch, computed, ref } from "vue";
import { useDocumentEditorStore } from "@/stores/documentEditor";

const props = withDefaults(defineProps<Props>(), {
  panelOpened: false,
});

const panelOpened = computed(() => props.panelOpened);
const editorDocStore = useDocumentEditorStore();
const handleRef = useTemplateRef<HTMLElement>("handleRef");
const { y: mouseY } = useMouse();

const startedPosition = ref(0);
const lastScrollPosition = ref(0);
const scrollY = ref(0);

const { pressed } = useMousePressed({ target: handleRef });
watch(pressed, (value) => {
  if (value) {
    startedPosition.value = mouseY.value;
  } else {
    lastScrollPosition.value = scrollY.value;
  }
});

// watch(() => editorDocStore.y, y => console.log('e', y))
const handleDrag = (mouseY) => {
    if (!pressed.value) return
    const value = (mouseY - startedPosition.value) + lastScrollPosition.value;
    scrollY.value = clamp(value, 0, editorDocStore.scrollArea);
    editorDocStore.setY(scrollY.value)
}

watch(mouseY, handleDrag)

const scrollT = () => {
    scrollY.value -= 10
}

const scrollB = () => {
    scrollY.value += 10
}

const style = computed(() => {
  return {
    top: scrollY.value + "px",
    height: editorDocStore.handleHeight,
  };
});
</script>
<style scoped>
@reference '~/assets/css/tailwind.css';
.scroll-wraper {
 
  @apply absolute flex flex-col w-4 h-full rtl:left-0 ltr:right-0 top-0 border-e border-s bg-gray-50 border-gray-200  ;
}
.scroll-holder {
  @apply relative w-full h-full ltr:rtl-text-align  border-white;
}
.scroll-handle {
  @apply absolute w-[10px]   bg-brandPrimary-400 border border-brandPrimary-300 rounded-2xl h-5 rtl:left-[15%] ltr:right-[15%] top-0;
}

.scroll-wraper:hover .scroll-handle,
.scroll-handle.pressed {
  @apply w-[10px] opacity-100 bg-brandPrimary-500;
}

.scroll-arrow {
    @apply flex items-center align-middle justify-center py-[2px] hover:bg-gray-200;
}
</style>

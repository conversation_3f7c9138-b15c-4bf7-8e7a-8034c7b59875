<template>
  <div>
    <div :class="['relative', borderClasses, { 'flex': stretch }]">
      <nav class="-mb-px flex space-x-8" :aria-label="ariaLabel" role="tablist">
        <button v-for="tab in tabs" :key="tab.value" @click="selectTab(tab.value)"
          @keydown="handleKeydown($event, tab.value)" :aria-controls="`tab-panel-${tab.value}`"
          :aria-selected="currentActiveTab === tab.value" :aria-setsize="tabs.length" :id="`tab-${tab.value}`"
          role="tab" type="button" :tabindex="currentActiveTab === tab.value ? 0 : -1" :class="[
            'whitespace-nowrap border-b-2 font-medium cursor-pointer',
            sizeClasses,
            'transition-colors duration-200 ease-in-out',
            stretch ? 'flex-1 text-center' : '',
            currentActiveTab === tab.value
              ? 'border-brandPrimary-500 text-gray-600 dark:border-brandPrimary-400 dark:text-gray-400'
              : errorTabs.includes(tab.value)
                ? 'border-brandDanger-500 text-brandDanger-600 dark:border-brandDanger-400 dark:text-brandDanger-400'
                : tabColorClasses,
            disabledTabs.includes(tab.value) ? 'opacity-50 cursor-not-allowed' : '',
          ]" :disabled="disabledTabs.includes(tab.value)">
          <Icon v-if="tab.icon" :name="tab.icon" size="calc(var(--spacing) * 4)" />
          {{ tab.label }}
          <Icon v-if="tab.error" name="solar:danger-circle-bold" size="calc(var(--spacing) * 4)" :class="[
            'inline-block ml-1', 'text-brandDanger-600 dark:text-brandDanger-400' 
          ]"   />
        </button>
      </nav>
    </div>

    <div class="mt-8" v-if="!hideContent">
      <div v-for="tab in tabs" :key="tab.value" v-show="currentActiveTab === tab.value" :id="`tab-panel-${tab.value}`"
        :aria-labelledby="`tab-${tab.value}`" role="tabpanel" tabindex="0"
        class="rounded-md">
     
        <slot :name="`panel-${tab.value}`" :activeTab="currentActiveTab"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';

export interface TabItem {
  label: string;
  value: string;
  icon?: string; // Optional: to add an icon to the tab
  disabled?: boolean; // Optional: to disable a tab
  error?: boolean;
}

const props = withDefaults(
  defineProps<{
    tabs: TabItem[];
    modelValue?: string; // v-model for controlled component
    variant?: 'underline' | 'pills'; // Future variants: 'pills', 'bordered', etc.
    color?: 'primary' | 'secondary' | 'gray' | 'danger' | 'warning' | 'success' | 'info' | string ; // Optional: custom color for inactive tabs
    stretch?: boolean; // If tabs should stretch to fill available width
    ariaLabel?: string; // Aria label for the tablist
    size?: 'sm' | 'md' | 'lg';
    hideBorder?: boolean;
    hideContent?: boolean;
  }>(),
  {
    tabs: () => [],
    modelValue: undefined,
    variant: 'underline',
    color: 'primary',
    stretch: false,
    ariaLabel: 'Tab navigation',
    size: 'md',
    hideBorder: false,
    hideContent: false,
  }
);

const emit = defineEmits(['update:modelValue', 'change']);

// Internal reactive state for the active tab, defaulting to the first tab's value
const internalActiveTab = ref<string>(props.tabs.length > 0 ? props.tabs[0].value : '');

const borderClasses = computed(() => {
  if (props.hideBorder) {
    return 'border-none';
  }
  return 'border-b border-gray-200 dark:border-gray-700';
});

// Determine the active tab based on modelValue (if provided) or internal state
const currentActiveTab = computed({
  get: () => props.modelValue !== undefined ? props.modelValue : internalActiveTab.value,
  set: (newValue) => {
    if (props.modelValue !== undefined) {
      // If using v-model, emit directly
      emit('update:modelValue', newValue);
    } else {
      // Otherwise, update internal state
      internalActiveTab.value = newValue;
    }
    emit('change', newValue); // Emit a general change event
  },
});

const disabledTabs = computed(() => props.tabs.filter(tab => tab.disabled).map(tab => tab.value));

const errorTabs = computed(() => props.tabs.filter(tab => tab.error).map(tab => tab.value));


const tabColorClasses = computed(() => {
  const colorMap = {
    primary: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-brandPrimary-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-brandPrimary-600',
    secondary: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-brandSecondary-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-brandSecondary-600',
    gray: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-gray-600',
    danger: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-red-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-red-600',
    warning: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-yellow-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-yellow-600',
    success: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-green-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-green-600',
    info: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-blue-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-blue-600',
  };

  if (colorMap.hasOwnProperty( props.color ) ) {
    return colorMap[props.color];
  }
  return !props.color ? colorMap.primary : `border-transparent text-${props.color}-500 hover:text-${props.color}-700 hover:border-${props.color}-300 dark:text-${props.color}-400 dark:hover:text-${props.color}-200 dark:hover:border-${props.color}-600`;
  
}); 

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'py-2 px-1 text-xs';
    case 'lg':
      return 'py-5 px-2 text-base';
    case 'md':
    default:
      return 'py-4 px-1 text-sm';
  }
});


 

// Set initial active tab on mount if modelValue is not set and tabs exist
onMounted(() => {
  if (props.modelValue === undefined && props.tabs.length > 0 && !internalActiveTab.value) {
    currentActiveTab.value = props.tabs[0].value;
  }
});

// Watch for changes in tabs prop to update active tab if current one becomes unavailable
watch(() => props.tabs, (newTabs) => {
  if (newTabs.length > 0 && !newTabs.some(tab => tab.value === currentActiveTab.value)) {
    // If the currently active tab is no longer in the list, switch to the first available tab
    currentActiveTab.value = newTabs[0].value;
  }
}, { deep: true });

const selectTab = (tabValue: string) => {
  if (!disabledTabs.value.includes(tabValue)) {
    currentActiveTab.value = tabValue;
  }
};

const handleKeydown = (event: KeyboardEvent, tabValue: string) => {
  const currentTabIndex = props.tabs.findIndex(tab => tab.value === tabValue);
  let nextTabIndex = currentTabIndex;

  switch (event.key) {
    case 'ArrowRight':
      event.preventDefault();
      nextTabIndex = (currentTabIndex + 1) % props.tabs.length;
      break;
    case 'ArrowLeft':
      event.preventDefault();
      nextTabIndex = (currentTabIndex - 1 + props.tabs.length) % props.tabs.length;
      break;
    case 'Home':
      event.preventDefault();
      nextTabIndex = 0;
      break;
    case 'End':
      event.preventDefault();
      nextTabIndex = props.tabs.length - 1;
      break;
    default:
      return; // Do not prevent default for other keys
  }

  const nextTab = props.tabs[nextTabIndex];
  if (nextTab && !disabledTabs.value.includes(nextTab.value)) {
    currentActiveTab.value = nextTab.value;
    // Focus the newly active tab button for improved accessibility
    requestAnimationFrame(() => {
      const el = document.getElementById(`tab-${nextTab.value}`);
      el?.focus();
    });
  }
};
</script>

<style scoped>
/* Tailwind CSS classes are primarily used inline or via computed properties. */
/* No additional <style scoped> is typically needed beyond what's already provided by Tailwind and component logic. */

/* Basic transitions for visual feedback (optional, but enhances UX) */
.tab-panel-enter-active,
.tab-panel-leave-active {
  transition: opacity 0.3s ease;
}
.tab-panel-enter-from,
.tab-panel-leave-to {
  opacity: 0;
}
</style>
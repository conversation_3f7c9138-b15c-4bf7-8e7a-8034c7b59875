import { ref, readonly } from 'vue';

type EventCallback = (...args: any[]) => void;
type EventBusListeners = Record<string, EventCallback[]>;

const listeners: EventBusListeners = {};

export function useEventBus() {
  const on = (event: string, callback: EventCallback) => {
    if (!listeners[event]) {
      listeners[event] = [];
    }
    listeners[event].push(callback);
  };

  const off = (event: string, callback: EventCallback) => {
    if (!listeners[event]) {
      return;
    }
    const index = listeners[event].indexOf(callback);
    if (index > -1) {
      listeners[event].splice(index, 1);
    }
  };

  const emit = (event: string, ...args: any[]) => {
    if (!listeners[event]) {
      return;
    }
    listeners[event].forEach(callback => {
      callback(...args);
    });
  };

  return {
    on,
    off,
    emit,
  };
}
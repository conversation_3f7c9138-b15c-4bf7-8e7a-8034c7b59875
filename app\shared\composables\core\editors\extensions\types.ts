// Type definitions for Tiptap Pages Extension

export interface PageMargins {
  top: number
  right: number
  bottom: number
  left: number
}

export interface PageDimensions {
  width: number
  height: number
}

export interface PageFormat extends PageDimensions {
  margins: PageMargins
}

export interface CustomPageFormat extends PageFormat {
  name: string
}

export interface HeaderFooterOptions {
  height: number
  content?: string
  showPageNumbers?: boolean
  showDocumentTitle?: boolean
  customContent?: string
  alignment?: 'left' | 'center' | 'right'
}

export interface PageLayoutOptions {
  headerHeight: number
  footerHeight: number
  pageGap: number
  pageBreakBackground: string
  showPageNumbers: boolean
  showHeaders: boolean
  showFooters: boolean
}

export interface PageOptions {
  format: string | CustomPageFormat
  headerHeight?: number
  footerHeight?: number
  pageGap?: number
  pageBreakBackground?: string
  showPageNumbers?: boolean
  showHeaders?: boolean
  showFooters?: boolean
  documentTitle?: string
  customHeader?: HeaderFooterOptions
  customFooter?: HeaderFooterOptions
}

export interface PageNodeAttributes {
  format: string
  pageNumber: number
  documentTitle?: string
  headerContent?: string
  footerContent?: string
  showPageNumbers: boolean
  showHeader: boolean
  showFooter: boolean
}

export interface PageBreakAttributes {
  type: 'auto' | 'manual'
  pageNumber: number
}

export interface PageContentAttributes {
  pageNumber: number
  overflow?: boolean
}

export interface PageHeaderAttributes {
  pageNumber: number
  documentTitle?: string
  customContent?: string
  alignment: 'left' | 'center' | 'right'
}

export interface PageFooterAttributes {
  pageNumber: number
  documentTitle?: string
  customContent?: string
  alignment: 'left' | 'center' | 'right'
  showPageNumbers: boolean
}

// Command types
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    tiptapPages: {
      /**
       * Insert a new page
       */
      insertPage: () => ReturnType
      /**
       * Insert a manual page break
       */
      insertPageBreak: () => ReturnType
      /**
       * Set page format
       */
      setPageFormat: (format: string | CustomPageFormat) => ReturnType
      /**
       * Set document title
       */
      setDocumentTitle: (title: string) => ReturnType
      /**
       * Toggle page numbers
       */
      togglePageNumbers: () => ReturnType
      /**
       * Toggle headers
       */
      toggleHeaders: () => ReturnType
      /**
       * Toggle footers
       */
      toggleFooters: () => ReturnType
      /**
       * Set header content
       */
      setHeaderContent: (content: string, alignment?: 'left' | 'center' | 'right') => ReturnType
      /**
       * Set footer content
       */
      setFooterContent: (content: string, alignment?: 'left' | 'center' | 'right') => ReturnType
      /**
       * Update page layout options
       */
      updatePageLayout: (options: Partial<PageLayoutOptions>) => ReturnType
    }
  }
}

<template>
  <div class="page-toolbar-tab">
    <div class="page-toolbar-col">
      <div class="flex">
        <Button
          label="Document Map"
          show-label
          icon-size="xl"
          icon="typcn:document-text"
          command="documentMap"
        />
      </div>
    </div>
    <Divider />
    <div class="page-toolbar-col">
      <div class="flex">
        <Button
          label="Page Settings"
          show-label
          icon-size="xl"
          icon="qlementine-icons:page-setup-16"
          @click="handlePageSettings"
        />
        <Button
          label="Margins"
          show-label
          icon-size="xl"
          icon="proicons:page-margins"
          command="eSignature"
        />

        <Button
          label="Orientation"
          show-label
          icon-size="xl"
          icon="fluent:orientation-24-regular"
          command="orientation"
        />
      </div>
    </div>

     <Divider />
    <div class="page-toolbar-col">
      <div class="flex">
        <Button
          label="Page Breaks"
          show-label
          icon-size="xl"
          icon="fluent:document-page-break-24-regular"
          command="pageBreak"
        />
        <Button
          label="Line Breaks"
          show-label
          icon-size="xl"
          icon="ci:line-break"
          command="lineBreak"
        />

        <Button
          label="Line Numbers"
          show-label
          icon-size="xl"
          icon="mdi-light:format-list-numbers"
          command="lineNumbers"
        />
      </div>
    </div>
    <Divider />
    <div class="page-toolbar-col">
      <div class="flex">
        <Button
          label="Preview"
          show-label
          icon-size="xl"
          icon="fluent:preview-link-24-regular"
          command="preview"
        />
       
      </div>
    </div>  
  </div>
</template>
<script setup lang="ts">
import Button from "./Button.vue"; // Import Button component
import Divider from "./Divider.vue"; // Import Divider component
import { useGlobalModal } from "~/composables/useGlobalModal.ts"; // Import globalModal composable
import PageSettingsModal from "../modals/PageSettings.vue";


const { openModal } = useGlobalModal(); // Get the openModal function from globalModal composable

const handlePageSettings = () => {
  // Add your logic for handling page settings here
 const modal = openModal({
  title: "Page Settings",
  component:PageSettingsModal,
  icon: "qlementine-icons:page-setup-16",
  okText: "OK",
  cancelText: "Cancel",
  onCancel () {
    modal.close();
  },
  onOk () {
    console.log("Page settings saved");
    
  }
 
 })
};

</script>

<style scoped>
@reference '~/assets/css/tailwind.css';
.page-toolbar-tab {
  @apply flex align-middle justify-center;
}
.page-toolbar-col {
  @apply flex flex-col space-y-2 align-middle justify-center;
}
</style>

import { Node, mergeAttributes } from '@tiptap/core'
import type { PageNodeAttributes } from './types'

export const PageNode = Node.create({
  name: 'page',
  
  group: 'block',
  content: 'pageHeader? pageContent pageFooter?',
  isolating: true,
  draggable: false,
  selectable: false,

  addAttributes() {
    return {
      format: {
        default: 'A4',
        parseHTML: element => element.getAttribute('data-format') || 'A4',
        renderHTML: attributes => ({
          'data-format': attributes.format,
        }),
      },
      pageNumber: {
        default: 1,
        parseHTML: element => parseInt(element.getAttribute('data-page-number') || '1'),
        renderHTML: attributes => ({
          'data-page-number': attributes.pageNumber,
        }),
      },
      documentTitle: {
        default: null,
        parseHTML: element => element.getAttribute('data-document-title'),
        renderHTML: attributes => {
          if (attributes.documentTitle) {
            return { 'data-document-title': attributes.documentTitle }
          }
          return {}
        },
      },
      headerContent: {
        default: null,
        parseHTML: element => element.getAttribute('data-header-content'),
        renderHTML: attributes => {
          if (attributes.headerContent) {
            return { 'data-header-content': attributes.headerContent }
          }
          return {}
        },
      },
      footerContent: {
        default: null,
        parseHTML: element => element.getAttribute('data-footer-content'),
        renderHTML: attributes => {
          if (attributes.footerContent) {
            return { 'data-footer-content': attributes.footerContent }
          }
          return {}
        },
      },
      showPageNumbers: {
        default: true,
        parseHTML: element => element.getAttribute('data-show-page-numbers') === 'true',
        renderHTML: attributes => ({
          'data-show-page-numbers': attributes.showPageNumbers.toString(),
        }),
      },
      showHeader: {
        default: true,
        parseHTML: element => element.getAttribute('data-show-header') === 'true',
        renderHTML: attributes => ({
          'data-show-header': attributes.showHeader.toString(),
        }),
      },
      showFooter: {
        default: true,
        parseHTML: element => element.getAttribute('data-show-footer') === 'true',
        renderHTML: attributes => ({
          'data-show-footer': attributes.showFooter.toString(),
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="page"]',
        priority: 100,
      },
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    const attrs = node.attrs as PageNodeAttributes
    
    return [
      'div',
      mergeAttributes(HTMLAttributes, {
        'data-type': 'page',
        'class': `tiptap-page tiptap-page-${attrs.format.toLowerCase()}`,
        'style': `
          position: relative;
          margin: 0 auto var(--page-gap, 50px);
          background: white;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          border-radius: 4px;
          overflow: hidden;
          page-break-after: always;
          break-after: page;
        `,
      }),
      0,
    ]
  },

  addCommands() {
    return {
      insertPage: () => ({ commands, state, editor }) => {
        const { selection } = state
        const { $from } = selection
        
        // Get current page number for the new page
        let pageNumber = 1
        state.doc.descendants((node, pos) => {
          if (node.type.name === 'page') {
            pageNumber = Math.max(pageNumber, (node.attrs.pageNumber || 0) + 1)
          }
        })

        // Get current document settings
        const currentPage = $from.node($from.depth)
        const format = currentPage?.attrs?.format || editor.storage.tiptapPages?.format || 'A4'
        const documentTitle = editor.storage.tiptapPages?.documentTitle || null
        const showPageNumbers = editor.storage.tiptapPages?.showPageNumbers ?? true
        const showHeader = editor.storage.tiptapPages?.showHeaders ?? true
        const showFooter = editor.storage.tiptapPages?.showFooters ?? true

        // Create new page with header, content, and footer
        const pageContent = {
          type: 'page',
          attrs: {
            format,
            pageNumber,
            documentTitle,
            showPageNumbers,
            showHeader,
            showFooter,
          },
          content: [
            ...(showHeader ? [{
              type: 'pageHeader',
              attrs: {
                pageNumber,
                documentTitle,
                alignment: 'center',
              },
            }] : []),
            {
              type: 'pageContent',
              attrs: { pageNumber },
              content: [{
                type: 'paragraph',
                content: [],
              }],
            },
            ...(showFooter ? [{
              type: 'pageFooter',
              attrs: {
                pageNumber,
                documentTitle,
                alignment: 'center',
                showPageNumbers,
              },
            }] : []),
          ],
        }

        return commands.insertContent(pageContent)
      },
    }
  },

  onCreate({ editor }) {
    // Initialize storage if not exists
    if (!editor.storage.tiptapPages) {
      editor.storage.tiptapPages = {
        format: 'A4',
        documentTitle: null,
        showPageNumbers: true,
        showHeaders: true,
        showFooters: true,
        headerHeight: 50,
        footerHeight: 50,
        pageGap: 50,
        pageBreakBackground: '#f6f3f4',
      }
    }
  },

  onUpdate({ editor }) {
    // Update page numbers when content changes
    let pageNumber = 1
    const tr = editor.state.tr
    let hasChanges = false

    editor.state.doc.descendants((node, pos) => {
      if (node.type.name === 'page') {
        if (node.attrs.pageNumber !== pageNumber) {
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            pageNumber,
          })
          hasChanges = true
        }
        pageNumber++
      }
    })

    if (hasChanges) {
      editor.view.dispatch(tr)
    }
  },
})

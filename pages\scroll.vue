<template>
  <div class=" space-y-6 py-6">
    <h1 class="text-3xl font-bold mb-8 text-center">Custom List Styles Demo</h1>
    <!-- Tab Navigation -->
    <div class="toolbar-tabs">
      <button v-for="tab in toolbarTabs" :key="tab.key" @click="activeTab = tab.key"
        :class="['toolbar-tab', { active: activeTab === tab.key }]">
        {{ tab.label }}
      </button>
    </div>
    <!-- Tab Content -->
    <div class="toolbar-content" v-show="showToolbar" @resize="resize">
      <a href="javascript:void(0)" @click="scrollLeft" class="toolbar-arrow toolbar-arrow-left" v-if="showLeftArrow">
        <Icon name="heroicons:chevron-left" />
      </a>
      <div class="toolbar-content-holder" ref="toolbarContentHolderRef">
        <template v-for="tab in toolbarTabs" :key="tab.key" >
          <component :is="TabsComponentMap[tab.key]" v-if="tab.key == activeTab" />
        </template>
      </div>
      <a href="javascript:void(0)" class="toolbar-arrow toolbar-arrow-right" @click="scrollRight" v-if="showRightArrow">
        <Icon name="heroicons:chevron-right" />
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, useTemplateRef, defineAsyncComponent } from "vue";
import { useScroll, useResizeObserver  } from "@vueuse/core";

// Page meta
definePageMeta({
  layout: "canvas",
});

const toolbarContentHolderRef = useTemplateRef<HTMLElement>("toolbarContentHolderRef");
const { x, measure, arrivedState } = useScroll(toolbarContentHolderRef, {
  behavior: "smooth",
});
const increaseBy = ref(0)
useResizeObserver(toolbarContentHolderRef, (entries) => {
  const entry = entries[0]
  const { width } = entry.contentRect
  increaseBy.value= width
 
})
const TabsComponentMap = {
  home: defineAsyncComponent(() => import("~/components/editors/document-editor/toolbar/Home.vue")),
  insert: defineAsyncComponent(() => import("~/components/editors/document-editor/toolbar/Insert.vue")),
  table: defineAsyncComponent(() => import("~/components/editors/document-editor/toolbar/Table.vue")),
  tools: defineAsyncComponent(() => import("~/components/editors/document-editor/toolbar/Tools.vue")),
  page: defineAsyncComponent(() => import("~/components/editors/document-editor/toolbar/Page.vue")),
  export: defineAsyncComponent(() => import("~/components/editors/document-editor/toolbar/Export.vue")),
};
const toolbarTabs = [
  { key: "home", label: "Home" },
  { key: "insert", label: "Insert" },
  { key: "table", label: "Table" },
  { key: "tools", label: "Tools" },
  { key: "page", label: "Page" },
  { key: "export", label: "Export" },
];
const activeTab = ref("home");
 
const showToolbar = ref(true);

const showLeftArrow = computed(() => {
  return !arrivedState.left;
});
const showRightArrow = computed(() => {
  return !arrivedState.right;
});

const scrollLeft = () => {
  if (showLeftArrow.value) {
    x.value -= increaseBy.value;
    // nextTick(() => measure());
  }
};

const scrollRight = () => {
  if (showRightArrow.value) {
    x.value += increaseBy.value;
    // nextTick(() => measure());
  }
};

const resize = (e) => {
  console.log(e);
  
};
</script>
<style scoped>
@reference '~/assets/css/tailwind.css';
.document-editor-toolbar {
  @apply bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex-1;
}

.toolbar-status-bar {
  @apply flex items-center justify-between px-4 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
}

.status-left {
  @apply flex items-center gap-2;
}

.status-indicator {
  @apply flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400;
}

.status-right {
  @apply flex items-center gap-2;
}

.toolbar-tabs {
  @apply flex items-center border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900;
}

.toolbar-tab {
  @apply px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 border-b-2 border-transparent transition-colors duration-200;
}

.toolbar-tab.active {
  @apply text-blue-600 dark:text-blue-400 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20;
}

.toolbar-content {
  @apply bg-gray-50 dark:bg-gray-800 relative   w-[100vw];
}
.toolbar-content-holder {
  @apply flex flex-col overflow-hidden relative w-[100vw];
}
.toolbar-arrow {
  @apply absolute top-[10%]  flex items-center justify-center w-6 h-[80%] rounded-sm shadow-xs border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-900 z-50 hover:bg-brandPrimary-500 hover:text-white hover:border-brandPrimary-600;
}

.toolbar-arrow-left {
  @apply left-2;
}

.toolbar-arrow-right {
  @apply right-2;
}

.toolbar-panel {
  @apply p-1 flex;
}

.toolbar-row {
  @apply flex items-start gap-4 mb-4 last:mb-0;
}

.toolbar-group {
  @apply flex flex-col gap-2;
}

.group-label {
  @apply text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide;
}

.group-buttons {
  @apply flex items-center gap-2;
}

.toolbar-separator {
  @apply w-px h-12 bg-gray-300 dark:bg-gray-600;
}
</style>

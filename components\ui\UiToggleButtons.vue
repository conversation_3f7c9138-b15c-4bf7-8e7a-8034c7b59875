<template>
  <div class="relative inline-flex items-center p-1 rounded-md bg-gray-100 dark:bg-gray-800">
    <div
      ref="slider"
      class="absolute top-1 left-1 bottom-1 bg-white dark:bg-gray-700 rounded-xs shadow-md transition-all duration-300 ease-in-out"
      :style="sliderStyle"
    ></div>
    <button
      v-for="(option, index) in options"
      :key="option.value"
      ref="buttons"
      @click="selectOption(option.value)"
      :class="[
        'relative z-10 px-4 py-2 text-sm font-medium transition-colors duration-300 ease-in-out rounded-md',
        
        {
          'text-gray-900 dark:text-white': modelValue === option.value,
          'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200': modelValue !== option.value,
          'cursor-not-allowed opacity-50': option.disabled,
        },
      ]"
      :disabled="option.disabled"
    >
      {{ option.label }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';

export interface ToggleOption {
  label: string;
  value: string;
  disabled?: boolean;
}

const props = withDefaults(
  defineProps<{
    options: ToggleOption[];
    modelValue: string;
  }>(),
  {
    options: () => [],
  }
);

const emit = defineEmits(['update:modelValue']);

const slider = ref<HTMLElement | null>(null);
const buttons = ref<HTMLButtonElement[]>([]);
const sliderStyle = ref({
  width: '0px',
  transform: 'translateX(0px)',
});

const selectOption = (value: string) => {
  const option = props.options.find(o => o.value === value);
  if (option && !option.disabled) {
    emit('update:modelValue', value);
  }
};

const updateSlider = () => {
  const activeIndex = props.options.findIndex(o => o.value === props.modelValue);
  if (activeIndex !== -1 && buttons.value[activeIndex]) {
    const activeButton = buttons.value[activeIndex];
    sliderStyle.value.width = `${activeButton.offsetWidth}px`;
    const parentPadding = 4; // p-1 which is 0.25rem = 4px
    sliderStyle.value.transform = `translateX(${activeButton.offsetLeft - parentPadding}px)`;
  }
};

onMounted(() => {
  nextTick(updateSlider);
});

watch(() => props.modelValue, () => {
  nextTick(updateSlider);
});

watch(() => props.options, () => {
    nextTick(updateSlider);
}, { deep: true });

</script>
{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "tailwind:init": "tailwindcss init -p", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "type-check": "nuxt typecheck", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "analyze": "ANALYZE=true nuxt build", "migrate:create-structure": "node tools/scripts/create-feature-structure.js", "migrate:update-imports": "node tools/scripts/update-imports.js", "i18n:validate": "node scripts/validate-translations.js"}, "dependencies": {"@floating-ui/vue": "^1.1.1", "@headlessui/vue": "^1.7.23", "@nuxt/icon": "^1.15.0", "@nuxt/image": "^1.10.0", "@nuxtjs/i18n": "^9.5.6", "@pinia/nuxt": "^0.11.1", "@tiptap/extension-collaboration": "^3.0.7", "@tiptap/extension-color": "^3.0.7", "@tiptap/extension-document": "^3.0.7", "@tiptap/extension-drag-handle": "^3.0.7", "@tiptap/extension-drag-handle-vue-3": "^3.0.7", "@tiptap/extension-font-family": "^3.0.7", "@tiptap/extension-highlight": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-list": "^3.0.7", "@tiptap/extension-list-item": "^3.0.7", "@tiptap/extension-node-range": "^3.0.7", "@tiptap/extension-paragraph": "^3.0.7", "@tiptap/extension-subscript": "^3.0.7", "@tiptap/extension-superscript": "^3.0.7", "@tiptap/extension-table": "^3.0.7", "@tiptap/extension-table-cell": "^3.0.7", "@tiptap/extension-table-header": "^3.0.7", "@tiptap/extension-table-row": "^3.0.7", "@tiptap/extension-task-item": "^3.0.7", "@tiptap/extension-task-list": "^3.0.7", "@tiptap/extension-text": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/extensions": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@tiptap/vue-3": "^3.0.7", "@tiptap/y-tiptap": "^3.0.0", "@unovis/ts": "^1.5.2", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.5.0", "@vueuse/nuxt": "^13.5.0", "axios": "^1.10.0", "change-case": "^5.4.4", "chart.js": "^4.5.0", "dayjs-nuxt": "^2.1.11", "form-data": "^4.0.3", "grapesjs": "^0.22.9", "grapesjs-blocks-basic": "^1.0.2", "grapesjs-component-countdown": "^1.0.2", "grapesjs-mjml": "^1.0.6", "grapesjs-plugin-export": "^1.0.12", "grapesjs-plugin-forms": "^2.0.6", "grapesjs-preset-newsletter": "^1.0.2", "grapesjs-preset-webpage": "^1.0.3", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "nuxt": "^3.17.6", "nuxt-charts": "^0.1.11", "pinia": "^3.0.3", "tiptap-extension-margin": "^1.0.0", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "y-protocols": "^1.0.6", "yjs": "^13.6.27", "zod": "^3.25.74"}, "devDependencies": {"@iconify-json/material-symbols": "^1.2.29", "@nuxt/eslint-config": "^1.2.0", "@nuxt/test-utils": "^3.19.2", "@tailwindcss/vite": "^4.1.11", "@types/form-data": "^2.5.2", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "^10.3.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vitest": "^3.2.4"}}
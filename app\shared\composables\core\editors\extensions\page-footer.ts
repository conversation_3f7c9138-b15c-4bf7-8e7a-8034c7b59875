import { Node, mergeAttributes } from '@tiptap/core'
import type { PageFooterAttributes } from './types'

export const PageFooter = Node.create({
  name: 'pageFooter',
  
  group: 'block',
  content: 'text*',
  isolating: true,
  draggable: false,
  selectable: true,

  addAttributes() {
    return {
      pageNumber: {
        default: 1,
        parseHTML: element => parseInt(element.getAttribute('data-page-number') || '1'),
        renderHTML: attributes => ({
          'data-page-number': attributes.pageNumber,
        }),
      },
      documentTitle: {
        default: null,
        parseHTML: element => element.getAttribute('data-document-title'),
        renderHTML: attributes => {
          if (attributes.documentTitle) {
            return { 'data-document-title': attributes.documentTitle }
          }
          return {}
        },
      },
      customContent: {
        default: null,
        parseHTML: element => element.getAttribute('data-custom-content'),
        renderHTML: attributes => {
          if (attributes.customContent) {
            return { 'data-custom-content': attributes.customContent }
          }
          return {}
        },
      },
      alignment: {
        default: 'center',
        parseHTML: element => element.getAttribute('data-alignment') || 'center',
        renderHTML: attributes => ({
          'data-alignment': attributes.alignment,
        }),
      },
      showPageNumbers: {
        default: true,
        parseHTML: element => element.getAttribute('data-show-page-numbers') === 'true',
        renderHTML: attributes => ({
          'data-show-page-numbers': attributes.showPageNumbers.toString(),
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="page-footer"]',
        priority: 100,
      },
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    const attrs = node.attrs as PageFooterAttributes
    
    return [
      'div',
      mergeAttributes(HTMLAttributes, {
        'data-type': 'page-footer',
        'class': `tiptap-page-footer tiptap-page-footer-${attrs.alignment}`,
        'style': `
          height: var(--footer-height, 50px);
          padding: 8px var(--page-margin-right, 20px) 8px var(--page-margin-left, 20px);
          border-top: 1px solid #e5e7eb;
          background: #f9fafb;
          display: flex;
          align-items: center;
          justify-content: ${attrs.alignment === 'left' ? 'flex-start' : attrs.alignment === 'right' ? 'flex-end' : 'center'};
          font-size: 12px;
          color: #6b7280;
          box-sizing: border-box;
        `,
      }),
      0,
    ]
  },

  addNodeView() {
    return ({ node, HTMLAttributes, getPos, editor }) => {
      const dom = document.createElement('div')
      const contentDOM = document.createElement('div')
      
      const attrs = node.attrs as PageFooterAttributes
      
      // Set up the container
      Object.assign(dom, {
        ...HTMLAttributes,
        'data-type': 'page-footer',
        className: `tiptap-page-footer tiptap-page-footer-${attrs.alignment}`,
      })
      
      // Apply styles
      dom.style.cssText = `
        height: var(--footer-height, 50px);
        padding: 8px var(--page-margin-right, 20px) 8px var(--page-margin-left, 20px);
        border-top: 1px solid #e5e7eb;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        color: #6b7280;
        box-sizing: border-box;
        position: relative;
      `
      
      // Create content areas
      const leftContent = document.createElement('div')
      const centerContent = document.createElement('div')
      const rightContent = document.createElement('div')
      
      leftContent.style.cssText = 'flex: 1; text-align: left;'
      centerContent.style.cssText = 'flex: 1; text-align: center;'
      rightContent.style.cssText = 'flex: 1; text-align: right;'
      
      // Set content based on attributes
      const documentTitle = attrs.documentTitle || editor.storage.tiptapPages?.documentTitle || ''
      const customContent = attrs.customContent || ''
      const showPageNumbers = attrs.showPageNumbers ?? true
      
      if (customContent) {
        // Use custom content
        if (attrs.alignment === 'left') {
          leftContent.textContent = customContent
        } else if (attrs.alignment === 'right') {
          rightContent.textContent = customContent
        } else {
          centerContent.textContent = customContent
        }
      } else {
        // Default footer content
        if (documentTitle) {
          leftContent.textContent = documentTitle
        }
        
        if (showPageNumbers) {
          rightContent.textContent = `Page ${attrs.pageNumber}`
        }
      }
      
      // Add editable content area
      contentDOM.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 8px var(--page-margin-right, 20px) 8px var(--page-margin-left, 20px);
        display: flex;
        align-items: center;
        justify-content: ${attrs.alignment === 'left' ? 'flex-start' : attrs.alignment === 'right' ? 'flex-end' : 'center'};
        background: transparent;
        z-index: 1;
      `
      
      // If there's no custom content, show the default content
      if (!node.textContent && !customContent) {
        dom.appendChild(leftContent)
        dom.appendChild(centerContent)
        dom.appendChild(rightContent)
      }
      
      dom.appendChild(contentDOM)

      return {
        dom,
        contentDOM,
      }
    }
  },

  addCommands() {
    return {
      setFooterContent: (content: string, alignment: 'left' | 'center' | 'right' = 'center') => ({ commands, state }) => {
        const { selection } = state
        const { $from } = selection
        
        // Find the current page
        let pagePos: number | null = null
        let footerPos: number | null = null
        
        state.doc.descendants((node, pos) => {
          if (node.type.name === 'page') {
            // Check if this page contains the current selection
            if (pos <= $from.pos && $from.pos <= pos + node.nodeSize) {
              pagePos = pos
              
              // Find the footer within this page
              node.descendants((childNode, childPos) => {
                if (childNode.type.name === 'pageFooter') {
                  footerPos = pos + childPos + 1
                  return false
                }
              })
              return false
            }
          }
        })
        
        if (footerPos !== null) {
          return commands.updateAttributes('pageFooter', {
            customContent: content,
            alignment,
          })
        }
        
        return false
      },
    }
  },
})

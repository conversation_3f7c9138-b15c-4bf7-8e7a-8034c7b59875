import { computed, ref, watch } from "vue";
import { Editor, EditorContent } from "@tiptap/vue-3";
// pre-built extinsions
import { Focus, CharacterCount  } from "@tiptap/extensions";
import StarterKit from "@tiptap/starter-kit";
import NodeRange from "@tiptap/extension-node-range";
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import { TextStyle, FontFamily, FontSize, LineHeight } from "@tiptap/extension-text-style";
import { TextAlign } from '@tiptap/extension-text-align'
import Image from '@tiptap/extension-image'
import { TableKit } from '@tiptap/extension-table'
// custom extinsions
import { Distribute } from "./distrbute";
import { Indent } from "./indent";
import { CustomBulletList, CustomOrderedList } from "./lists";
import Columns from "./columns";
 

export interface DocEditor extends Editor {

}




const editor = ref<DocEditor | null>(null);

const InsertItems = ref([
  {
    name: "Template",
    icon: "i-icon-park-outline:page-template",
    command: () => { },
    category: "content",
  },
  {
    name: "Table",
    icon: "heroicons:table-cells",
    command: () => { },
    category: "content",
  },
  {
    name: "Image",
    icon: "heroicons:photo",
    command: () => { },
    category: "media",
  },
  {
    name: "Divider",
    icon: "heroicons:minus",
    command: () => { },
    category: "content",
  },
  {
    name: "Page Break",
    icon: "fluent:document-page-break-24-regular",
    command: () => { },
    category: "layout",
  },
  {
    name: "Line Break",
    icon: "ci:line-break",
    command: () => { },
    category: "layout",
  },
]);

export const useDocEditor = () => {
  const extensions = [
    StarterKit.configure({
      orderedList: false,
      bulletList: false,

      // Configure an included extension
      heading: {
        levels: [1, 2, 3, 4, 5, 6],
      },
    }),
    Focus,
    NodeRange.configure({
      // allow to select only on depth 0
      // depth: 0,
      key: null,
    }),
    Subscript,
    Superscript,
    TextStyle,
    FontFamily,
    FontSize,
    LineHeight,
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    Distribute.configure({
      types: ['heading', 'paragraph'],
    }),
    Indent.configure({
      types: ['heading', 'paragraph', 'listItem'],
      indentSize: 24,
      maxIndent: 5,
    }),
    CustomBulletList,
    CustomOrderedList,
    Image,
    TableKit,
    Columns,
    CharacterCount 
  ];

  const initializeEditor = (content = "") => {
    if (editor.value) return;

    editor.value = new Editor({
      content,
      extensions,
    });

  };

  const destroyEditor = () => {
    if (editor.value) {
      editor.value?.destroy();
      editor.value = null;
    }
  };

  const executeCommand = (commandName: string, params?: any) => {
    if (!editor.value) {
      console.warn("Editor not initialized");
      return;
    }

    try {
      switch (commandName) {
        case "bold":
          editor.value.chain().focus().toggleBold().run();
          break;
        case "italic":
          editor.value.chain().focus().toggleItalic().run();
          break;
        case "underline":
          editor.value.chain().focus().toggleUnderline().run();
          break;
        case "strikethrough":
          editor.value.chain().focus().toggleStrike().run();
          break;
        case "superscript":
          editor.value.chain().focus().toggleSuperscript().run();
          break;
        case "subscript":
          editor.value.chain().focus().toggleSubscript().run();
          break;
        case "clearFormatting":
          editor.value.chain().focus().clearNodes().unsetAllMarks().run();
          break;
        case "fontFamily":
          editor.value.chain().focus().setFontFamily(params).run();
          break;
        case "fontSize":
          editor.value.chain().focus().setFontSize(params + "px").run();
          break;
        case "textAlign":

          editor.value.chain().focus().setTextAlign(params).run();
          break;
        case "selectAll":
          editor.value.chain().focus().selectAll().run();
          break;

        case "heading":
          if (params === 0)
            editor.value.chain().focus().setParagraph().run();
          else
            editor.value.chain().focus().toggleHeading({ level: params }).run();
          break;
        case "distribute":
          editor.value.chain().focus().distribute().run();
          break;
        case "indent":
          editor.value.chain().focus().indent().run();
          break;
        case "outdent":
          editor.value.chain().focus().outdent().run();
          break;
        case "bulletList":
          handleCustomList({ command: "bulletList", value: "list-bullet-filled-circle" })
          break;
        case "orderedList":
          handleCustomList({ command: "orderedList", value: "list-ordered-numbers" })
          break;
        case "list":
          handleCustomList(params); // Pass the params to the handleCustomList function
          break;
        case "undo":
          editor.value.chain().focus().undo().run()
          break;
        case "redo":
          editor.value.chain().focus().redo().run()
          break;
        case "lineHeight":
         editor.value.chain().focus().toggleTextStyle({ lineHeight: params?.value }).run()
          // editor.value.chain().focus().setLineHeight(params?.value + '')
        
          break;
        case 'unsetLink':
          editor.value.chain().focus().extendMarkRange("link").unsetLink().run();
          break;
        case 'setLink':
          editor.value.chain().focus().extendMarkRange('link').setLink(params).run();
          break;

        case 'setImage':
          editor.value.chain().focus().setImage(params).run();
          break;
        case 'insertTable':
          editor.value.chain().focus().insertTable(params).run();
          break;
        case 'insertColumns':
          editor.value.chain().focus().insertColumns(params).run();
          break;
        case 'lineBreak':
          editor.value.chain().focus().setHardBreak().run();
          break;
        case 'divider':
          editor.value.chain().focus().setHorizontalRule().run()
          break;


        default: {
          if (typeof editor.value.chain()[commandName] == 'function')
            editor.value.chain().focus()[commandName](params).run()
          else
            console.warn(`Unknown command: ${commandName}`);
        }

      }
    } catch (error) {
      console.error("Error executing command:", error);
    }
  };

  const handleCustomList = ({ command, value }: { command: string, value: string }) => {
    if (!editor.value) {
      console.warn("Editor not initialized");
      return;
    }
    try {
      switch (command) {
        case "bulletList":
          const isListActive = editor.value.isActive('bulletList');
          const currentAttributes = editor.value.getAttributes('bulletList');

          if (isListActive && currentAttributes.class === value) {
            editor.value.chain().focus().toggleBulletList().run();
          } else {
            if (!isListActive) {
              editor.value.chain().focus().toggleBulletList().run();
            }
            editor.value.chain().focus().updateAttributes('bulletList', { class: value }).run();
          }
          break;
        case "orderedList":
          const isOListActive = editor.value.isActive('orderedList');
          const currentOAttributes = editor.value.getAttributes('orderedList');

          if (isOListActive && currentOAttributes.class === value) {
            editor.value.chain().focus().toggleOrderedList().run();
          } else {
            if (!isOListActive) {
              editor.value.chain().focus().toggleOrderedList().run();
            }
            editor.value.chain().focus().updateAttributes('orderedList', { class: value }).run();
          }

          break;

        default:
          console.warn(`Unknown command: ${command}`);
      }
    } catch (error) {
      console.error("Error executing command:", error);
    }


  };

  const hasSelection = computed(()=> {
    if (!editor.value) return false
    const state = editor.value.state;
    const selection = state.selection;
    const { from, to } = selection
    return !!state.doc.textBetween(from, to, ' ')
   
    

  })
 
  return {
    EditorContent,
    editor,
    initializeEditor,
    destroyEditor,
    InsertItems,
    executeCommand,
    hasSelection
  };
};

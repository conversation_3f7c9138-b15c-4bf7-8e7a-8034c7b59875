<template>
  <div class="document-editor-container">
    <div class="document-canvas" ref="documentCanvasRef">
    <GptEditorDocumentsScrollBar :panel-opened="panelOpened" v-if="editorDocStore.hasScroll && false" />
      <div v-if="editor"
        class="document-page"
      >
        
        <DragHandle :editor="editor">
          <div class="flex space-x-1 align-middle justify-center bg-gray-50">
            <ui-dropdown width="40" @open="handleOpen" @close="handleClose">
              <template #trigger="{ toggle }">
                <ui-button variant="flat" size="xs" @click="toggle">
                  <Icon
                    name="heroicons:plus"
                    size="calc(var(--spacing) * 4)"
                    class="text-gray-600"
                  />
                </ui-button>
              </template>
              <template #content="{ close }">
                <div class="py-2">
                  <div class="flex items-center space-x-2 mb-2">
                    <h3 class="text-sm font-medium text-gray-500 px-3">Insert</h3>
                  </div>
                  <ul>
                    <li v-for="item in InsertItems" :key="item.name">
                      <ui-button
                        variant="flat"
                        color="gray"
                        :elevated="false"
                        class="w-full justify-start"
                        size="sm"
                        @click="item.command()"
                        :leading-icon="item.icon"
                      >
                        {{ item.name }}
                      </ui-button>
                    </li>
                  </ul>
                </div>
              </template>
            </ui-dropdown>
            <div class="custom-drag-handle" v-if="!editor.isEmpty" />
          </div>
        </DragHandle>
 
        <EditorContent :editor="editor" class="prose max-w-none" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
interface Props {
  sidebarOpened?: boolean;
  panelOpened?: boolean
}

import "~/assets/css/list-styles.css";
import { computed, onMounted, onUnmounted, watch} from "vue";
import { useI18n } from "vue-i18n";
import { useDocEditor } from "~/app/shared/composables/core/editors/useDocEditor";
import { DragHandle } from "@tiptap/extension-drag-handle-vue-3";
import { useEditorScroll } from './../composables/useEditorScroll'
import { useDocumentEditorStore } from "@/stores/documentEditor";

const props = withDefaults(defineProps<Props>(), {
  sidebarOpened: false,
  panelOpened: false
})
const panelOpened = computed(() => props.panelOpened)
const editorDocStore = useDocumentEditorStore();

const scrollY = computed(() => editorDocStore.y)

useEditorScroll('documentCanvasRef')
 
const { t } = useI18n();
const { editor, initializeEditor, InsertItems, EditorContent, destroyEditor } = useDocEditor();

const content = `
                <h1>${t("documents.editor.document.welcomeTitle")}</h1>
                <p>${t("documents.editor.document.welcomeMessage")}</p>
                <p>${t("documents.editor.document.startTyping")}</p>
              `;

const handleOpen = () => {
  editor.value?.commands?.setMeta("lockDragHandle", true);
};

const handleClose = () => {
  editor.value?.commands?.setMeta("lockDragHandle", false);
};

 

onMounted(() => initializeEditor(content));
onUnmounted(destroyEditor);
</script>
<style >
@reference "~/assets/css/tailwind.css";
.document-editor-container {
  @apply relative w-full h-full bg-gray-100 dark:bg-gray-900 overflow-hidden;
}
.document-canvas a {
  @apply text-brandPrimary;
}
.document-canvas {
  @apply w-full h-full overflow-auto bg-gray-100 dark:bg-gray-900;
  scroll-behavior: smooth;
  /* Dynamic padding based on zoom level to ensure document is always visible */
  padding: calc(2rem * var(--zoom-factor, 1));
  display: flex;
  justify-content: center;
  align-items: flex-start;
  /* Minimum height to accommodate scaled document */
  /* min-height: calc(100vh + 200px); */
}

.document-page {
  @apply bg-white dark:bg-gray-800 shadow-lg relative;
  /* box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); */
  /* border: 1px solid rgba(0, 0, 0, 0.1); */
  /* Fixed dimensions - zoom will be handled by transform scale */
  width: 816px; /* A4 width */
  min-height: 1056px; /* A4 height */
  /* Ensure proper spacing around scaled document */
  margin: 2rem auto;
}
</style>
